from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from anticaptchaofficial.imagecaptcha import imagecaptcha
from twocaptcha import TwoCaptcha
import os, random, logging, json, psutil, secrets, requests, base64, tempfile, time
import shutil, msvcrt, sys, re
from random import randint, uniform
from time import sleep
import pyautogui
import numpy as np

# OpenCV library for visual template matching
try:
    import cv2
    CV2_AVAILABLE = True
    print("OpenCV library imported successfully")
except ImportError as e:
    print(f"OpenCV library not available: {str(e)}")
    CV2_AVAILABLE = False

# PIL library for image processing
try:
    from PIL import Image, ImageGrab
    PIL_AVAILABLE = True
    print("PIL library imported successfully")
except ImportError as e:
    print(f"PIL library not available: {str(e)}")
    PIL_AVAILABLE = False




"""
Groups Management System with Enhanced SeleniumBase Integration
Phase 2.1: Migration from selenium-wire to SeleniumBase with advanced stealth capabilities

CHROME FLAGS UPDATE:
- Removed deprecated --ignore-certificate-errors flag (unsupported in newer Chrome versions)
- Using modern alternatives: --ignore-ssl-errors, --disable-certificate-transparency
- Updated for Chrome 120+ compatibility

SECURITY ALERT HANDLING:
- Enhanced detection and handling of Google's "Critical security alert" popups
- Automatic handling of suspicious sign-in activity warnings
- Intelligent response to security review pages after clicking "Check activity"
- Supports both English and French language interfaces

LANGUAGE MANAGEMENT:
- Automatic Gmail language change to French on first login (FIRST PRIORITY after login)
- Tracks language change status in both Gmail accounts map and profile configuration
- Prevents repeated language change attempts for already configured accounts
- Supports multiple methods for language detection and change (dropdown, URL params, JavaScript)
- Uses direct Google Account settings page for reliable language changes
"""

# Enhanced SeleniumBase Driver - Primary Interface
try:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    from updated_groups import EnhancedSeleniumBaseDriver, ProfileManager, SessionManager, BehavioralSimulator
    ENHANCED_DRIVER_AVAILABLE = True
    print("Enhanced SeleniumBase Driver and components imported successfully")
except ImportError as e:
    print(f"Enhanced driver not available: {str(e)}")
    print("Migration cannot proceed without enhanced driver components")
    ENHANCED_DRIVER_AVAILABLE = False
    # Create placeholder classes to prevent import errors
    class EnhancedSeleniumBaseDriver:
        def __init__(self, *args, **kwargs):
            raise ImportError("Enhanced driver not available")
    class ProfileManager:
        def __init__(self, *args, **kwargs):
            raise ImportError("Enhanced driver not available")
    class SessionManager:
        def __init__(self, *args, **kwargs):
            raise ImportError("Enhanced driver not available")
    class BehavioralSimulator:
        def __init__(self, *args, **kwargs):
            raise ImportError("Enhanced driver not available")

# Enhanced Proxy Manager removed - using simple extension-based proxy only

# 5sim Integration for Phone Verification
try:
    from fivesim_integration import FiveSimClient, FiveSimError, FiveSimManager, load_fivesim_config
    FIVESIM_AVAILABLE = True
    print("5sim integration module imported successfully")
except ImportError as e:
    print(f"5sim integration not available: {str(e)}")
    FIVESIM_AVAILABLE = False



from datetime import datetime
from random import choice, randint, uniform
from unidecode import unidecode
from string import digits, ascii_letters, punctuation
import secrets



os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
Files_home = home.replace('PyFiles','')
profile_home = f"{home.replace('PyFiles','')}Profiles"
data_file = f"{home.replace('PyFiles','')}data.txt"
print(data_file)
gmail_account_file = f"{home.replace('PyFiles','')}Gmail_Accounts"
data_directory = f"{home}/Data"
gmail_map_file = f"{home}/json/GmailAccountsMap.json"
map_path = f"{home}/json/GroupsMap.json"
dead_accounts = f"{home}/DeadAccounts.txt"
ua_map = f"{home}/json/ua-lib.json"
proxy_file = f"{home}/proxy.txt"
settings_path = f"{home}/json/settings.json"
cp_xpaths = [
    "//div[@id='rc-anchor-container']",
    "//div[@id='recaptcha-accessible-status']",
    "//span[contains(@class, 'recaptcha-checkbox')]",
    
]

# Image CAPTCHA XPaths for Gmail login flow
image_captcha_xpaths = [
    "//img[@id='captchaimg']",
    "//div[contains(@jscontroller, 'CMcBD')]//img",
    "//input[@name='ca']"
]


# ProxyBridge class removed - using simple extension-based proxy only


class CityName():
    def __init__(self) -> None:
        self.country_codes = ["US","AU","GB"]
        self.cd = secrets.choice(self.country_codes)

    def v1(self):
            url = 'https://countriesnow.space/api/v0.1/countries/population/cities'
            resp = requests.get(url,params={"country":f"{self.cd}"}).text
            print(resp)
            info = json.loads(resp)
            return info
        
        
    def v2(self):
        url = "https://wft-geo-db.p.rapidapi.com/v1/geo/cities"
        querystring = {"types":"CITY","countryIds":f"{self.cd}","minPopulation":"1000","maxPopulation":"10000000","limit":"10","offset":f"{randint(0,1000)}","sort":"population"}
        headers = {
            'x-rapidapi-host': "wft-geo-db.p.rapidapi.com",
            'x-rapidapi-key': "**************************************************"
            }
        resp = requests.get(url, headers=headers, params=querystring).text
        info = json.loads(resp)
        return info
    
    
    
    
    def name(self):
        accents = ['à' ,'â', 'ä','é','è','ê' ,'ë' ,'ú' ,'ï'  ,'î' ,'ô' , 'ó'  ,'ö'  ,'ù'  ,'û' ,'ü' ,'ÿ' ,'ç','í','á', 'ạ' ,'à', 'ả', 'ã'
                'ă', 'ắ' ,'ặ', 'ằ' ,'ẳ','ẵ',
                'â', 'ấ' ,'ậ' ,'ầ', 'ẩ ','ẫ',
                'é', 'ẹ', 'è', 'ẻ', 'ẽ',
                'ê' ,'ế' ,'ệ', 'ề' ,'ể', 'ễ'
                'i' ,'í' ,'ị' ,'ì' ,'ỉ', 'ĩ']
        try:
            self.data = self.v2()
            city = self.data["data"][randint(0, 9)]["city"].lower()
        except:
            self.data = self.v1()
            lim = randint(1, 500)
            city = self.data['data'][lim]['city'].lower()
           
        city = city.replace(" ","").replace("-","").replace("@","").replace(",","").replace(";","").replace("'","").replace("(","").replace(")","")
        accents_contains = any(accent in city for accent in accents)
        if accents_contains:
            city = unidecode(city)
        city = f"{city.capitalize()}{randint(1000,9999)}"
        return city



class Driver():
    """
    Fully Migrated Driver class - Uses EnhancedSeleniumBaseDriver exclusively
    Provides backward compatibility while leveraging all enhanced features
    """
    def __init__(self, email, password, ua_agent, index):
        # Setup logging
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger(f"EnhancedDriver-{email}")

        # Store credentials and parameters
        self.email = email
        self.password = password
        self.ua_agent = ua_agent
        self.index = index
        self.url = None

        # Ensure enhanced driver is available
        if not ENHANCED_DRIVER_AVAILABLE:
            error_msg = "Enhanced SeleniumBase Driver is required but not available. Please ensure updated_groups.py is accessible."
            self.logger.error(error_msg)
            raise ImportError(error_msg)

        try:
            self.logger.info("Initializing Enhanced SeleniumBase Driver with full feature set")

            # Create enhanced driver instance
            self._enhanced_driver = EnhancedSeleniumBaseDriver(email, password, ua_agent, index)

            # Expose browser for backward compatibility
            self.browser = self._enhanced_driver.browser

            # Log profile and session information
            if hasattr(self._enhanced_driver, 'profile_config'):
                profile_info = self._enhanced_driver.profile_config
                self.logger.info(f"Profile ID: {profile_info.get('profile_id', 'unknown')}")
                self.logger.info(f"Profile path: {profile_info.get('profile_path', 'unknown')}")

            if hasattr(self._enhanced_driver, 'session_manager') and self._enhanced_driver.session_manager:
                self.logger.info("Session management enabled")

            if hasattr(self._enhanced_driver, 'behavioral_simulator'):
                self.logger.info("Behavioral simulation enabled")

            self.logger.info("Enhanced SeleniumBase Driver initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Enhanced SeleniumBase Driver: {str(e)}")
            raise e

    # Enhanced driver handles all proxy configuration internally

    def __del__(self):
        """Cleanup when Driver object is destroyed"""
        try:
            if hasattr(self, '_enhanced_driver') and self._enhanced_driver:
                # Enhanced driver handles its own cleanup
                self._enhanced_driver.finish()
                self.logger.info("Enhanced driver cleanup completed")
        except Exception as e:
            # Use print instead of logger since logger might be destroyed
            print(f"Error during Driver cleanup: {str(e)}")

    def __getattr__(self, name):
        """
        Delegate method calls to enhanced driver for backward compatibility
        This ensures all enhanced driver methods are accessible through the Driver instance
        """
        if hasattr(self, '_enhanced_driver') and hasattr(self._enhanced_driver, name):
            return getattr(self._enhanced_driver, name)
        else:
            # Method not found in enhanced driver
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")




class Worker():
    def __init__(self,actions):
        super().__init__()
        self.actions = actions
        self.browser = None  # Will be set when Driver is created
        self._enhanced_driver = None  # Will be set when Driver is created
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger("Worker")

    def _setup_driver_references(self, driver_instance):
        """Set up references to the driver and enhanced driver for Worker methods"""
        self.browser = driver_instance
        if hasattr(driver_instance, '_enhanced_driver'):
            self._enhanced_driver = driver_instance._enhanced_driver
        else:
            self._enhanced_driver = None
            self.logger.warning("Enhanced driver not available in Driver instance")

    

    def _update_suspicious_activity_status(self, email, status=True):
        """
        Update the suspicious activity cleared status in both Gmail accounts map and profile configuration.
        This ensures tracking is maintained across both systems independently.
        """
        try:
            # Update Gmail accounts map
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for item in data:
                if item['email'] == email:
                    item['suspicious_activity_cleared'] = status
                    if status:
                        item['suspicious_activity_cleared_date'] = datetime.now().isoformat()
                    break
            else:
                # Account not found, add it
                data.append({
                    'email': email,
                    'password': '',  # Will be updated elsewhere
                    'ua': '',
                    'email_conf': '',
                    'phone': '',
                    'status': 'active',
                    'suspicious_activity_cleared': status,
                    'suspicious_activity_cleared_date': datetime.now().isoformat() if status else None
                })

            with open(gmail_map_file, 'w') as f:
                json.dump(data, f, indent=4)

            self.logger.info(f"Updated suspicious activity status in Gmail map for {email}: {status}")

            # Also update profile configuration if enhanced driver is available
            try:
                if hasattr(self, 'browser') and hasattr(self.browser, 'profile_manager'):
                    profile_id = self.browser.profile_manager._generate_profile_id(email)
                    if profile_id in self.browser.profile_manager.profiles_config:
                        profile = self.browser.profile_manager.profiles_config[profile_id]
                        if 'account_settings' not in profile:
                            profile['account_settings'] = {}
                        profile['account_settings']['suspicious_activity_cleared'] = status
                        profile['account_settings']['suspicious_activity_cleared_date'] = datetime.now().isoformat()
                        self.browser.profile_manager._save_profiles_config()
                        self.logger.info(f"Updated profile configuration for suspicious activity clearing: {email}")
            except Exception as profile_error:
                self.logger.debug(f"Could not update profile configuration for suspicious activity: {str(profile_error)}")

        except Exception as e:
            self.logger.error(f"Error updating suspicious activity status: {str(e)}")



    def _find_element_silent(self, selector, selector_type='xpath', timeout=5):
        """Helper method to find element without throwing exceptions"""
        try:
            if selector_type == 'xpath':
                return WebDriverWait(self.browser, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
            elif selector_type == 'css':
                return WebDriverWait(self.browser, timeout).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
            else:
                return None
        except:
            return None



    def _find_elements_silent(self, selector, selector_type='xpath', timeout=5):
        """Helper method to find multiple elements without throwing exceptions"""
        try:
            if selector_type == 'xpath':
                WebDriverWait(self.browser, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                return self.browser.find_elements(By.XPATH, selector)
            elif selector_type == 'css':
                WebDriverWait(self.browser, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                return self.browser.find_elements(By.CSS_SELECTOR, selector)
            else:
                return []
        except:
            return []




    def _log_page_debugging_info(self):
        """Add debugging information about the current page structure"""
        try:
            self.logger.info("PAGE DEBUGGING INFORMATION:")

            # Log current URL
            current_url = self.browser.this_url()
            self.logger.info(f"Current URL: {current_url}")

            # Log page title
            try:
                page_title = self.browser.title
                self.logger.info(f"Page title: {page_title}")
            except:
                self.logger.info("Page title: Unable to retrieve")

            # Log all clickable elements found on the page
            self._log_all_clickable_elements()

            # Log Chrome custom elements
            self._log_chrome_custom_elements()

        except Exception as e:
            self.logger.debug(f"Page debugging failed: {str(e)}")



    def _log_all_clickable_elements(self):
        """Log all clickable elements detected on the page"""
        try:
            self.logger.info("ALL CLICKABLE ELEMENTS:")

            clickable_selectors = [
                ('cr-button', 'Chrome buttons'),
                ('button', 'Regular buttons'),
                ('//*[@role="button"]', 'Role-based buttons'),
                ('//div[@role="button"]', 'Div buttons'),
                ('//a', 'Links')
            ]

            total_clickable = 0

            for selector, description in clickable_selectors:
                try:
                    if selector.startswith('//'):
                        elements = self.browser.find_elements(By.XPATH, selector)
                    else:
                        elements = self.browser.find_elements(By.CSS_SELECTOR, selector)

                    visible_elements = [elem for elem in elements if elem.is_displayed()]
                    total_clickable += len(visible_elements)

                    self.logger.info(f"  - {description}: {len(visible_elements)} visible ({len(elements)} total)")

                    # Log first few elements with their text
                    for i, elem in enumerate(visible_elements[:3], 1):
                        try:
                            elem_text = self._get_element_text_content(elem)[:50]
                            elem_id = elem.get_attribute('id') or 'no-id'
                            elem_class = elem.get_attribute('class') or 'no-class'
                            self.logger.debug(f"    [{i}] Text: '{elem_text}' | ID: {elem_id} | Class: {elem_class}")
                        except:
                            continue

                except Exception as e:
                    self.logger.debug(f"Failed to check {description}: {str(e)}")

            self.logger.info(f"Total visible clickable elements: {total_clickable}")

        except Exception as e:
            self.logger.debug(f"Clickable elements logging failed: {str(e)}")



    def _log_chrome_custom_elements(self):
        """Log Chrome custom elements found on the page"""
        try:
            self.logger.info("CHROME CUSTOM ELEMENTS:")

            custom_elements = [
                'settings-subpage',
                'settings-sync-page',
                'settings-sync-account-control',
                'cr-button',
                'cr-toggle',
                'settings-section'
            ]

            for element_type in custom_elements:
                try:
                    elements = self.browser.find_elements(By.CSS_SELECTOR, element_type)
                    if elements:
                        self.logger.info(f"  - {element_type}: {len(elements)} found")

                        # Log attributes of first element
                        if elements:
                            first_elem = elements[0]
                            try:
                                # Get common attributes
                                attrs = {}
                                for attr in ['id', 'class', 'page-title', 'promo-label-with-account']:
                                    value = first_elem.get_attribute(attr)
                                    if value:
                                        attrs[attr] = value[:50]  # Truncate long values

                                if attrs:
                                    attr_str = ', '.join(f"{k}='{v}'" for k, v in attrs.items())
                                    self.logger.debug(f"    First element attributes: {attr_str}")

                            except:
                                pass

                except Exception as e:
                    self.logger.debug(f"Failed to check {element_type}: {str(e)}")

        except Exception as e:
            self.logger.debug(f"Chrome custom elements logging failed: {str(e)}")



    def _get_element_text_content(self, element):
        """
        Get text content from element, handling Chrome custom elements and shadow DOM

        Args:
            element: WebElement to extract text from

        Returns:
            str: Text content of the element
        """
        try:
            # Try multiple methods to get text content
            text_methods = [
                lambda e: e.text,
                lambda e: e.get_attribute('textContent'),
                lambda e: e.get_attribute('innerText'),
                lambda e: e.get_attribute('value'),
                lambda e: e.get_attribute('aria-label'),
                lambda e: e.get_attribute('title')
            ]

            for method in text_methods:
                try:
                    text = method(element)
                    if text and text.strip():
                        return text.strip()
                except:
                    continue

            return ""

        except Exception as e:
            self.logger.debug(f"Error getting element text: {str(e)}")
            return ""


    def _find_element_silent(self, selector, by_type='xpath', timeout=3):
        """
        Find element without logging errors for failed attempts
        Used during sync button detection to avoid spam logs
        """
        try:
            if by_type.lower() == 'xpath':
                element = WebDriverWait(self.browser, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
            elif by_type.lower() == 'css':
                element = WebDriverWait(self.browser, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
            else:
                return None

            return element if element.is_displayed() else None

        except:
            return None



    def _get_browser_window_coordinates(self):
        """Get browser window coordinates for coordinate-based clicking"""
        try:
            # Try to get window position using JavaScript
            try:
                if hasattr(self.browser, 'execute_js'):
                    window_info = self.browser.execute_js("""
                        return {
                            x: window.screenX || window.screenLeft || 0,
                            y: window.screenY || window.screenTop || 0,
                            width: window.outerWidth || 1200,
                            height: window.outerHeight || 800
                        };
                    """)
                elif hasattr(self.browser, 'execute_script'):
                    window_info = self.browser.execute_script("""
                        return {
                            x: window.screenX || window.screenLeft || 0,
                            y: window.screenY || window.screenTop || 0,
                            width: window.outerWidth || 1200,
                            height: window.outerHeight || 800
                        };
                    """)
                else:
                    window_info = None

                if window_info:
                    self.logger.debug(f"Browser window coordinates: {window_info}")
                    return window_info

            except Exception as js_error:
                self.logger.debug(f"JavaScript window detection failed: {str(js_error)}")

            # Fallback: assume standard browser window position
            return {
                'x': 100,
                'y': 100,
                'width': 1200,
                'height': 800
            }

        except Exception as e:
            self.logger.debug(f"Window coordinate detection failed: {str(e)}")
            return None



    def _click_button_by_attributes(self, button_element):
        """Try to click a button using its attributes to build XPath/CSS selectors"""
        try:
            # Build selectors based on button attributes
            selectors = []

            # Try ID-based selector
            button_id = button_element.get('id')
            if button_id:
                selectors.append(f'//*[@id="{button_id}"]')
                selectors.append(f'#{button_id}')

            # Try class-based selector
            button_classes = button_element.get('class', [])
            if button_classes:
                class_str = ' '.join(button_classes)
                selectors.append(f'//*[@class="{class_str}"]')
                selectors.append(f'.{".".join(button_classes)}')

            # Try text-based selector
            button_text = button_element.get_text(strip=True)
            if button_text:
                tag_name = button_element.name
                selectors.append(f'//{tag_name}[contains(text(), "{button_text}")]')
                selectors.append(f'//{tag_name}[normalize-space(.)="{button_text}"]')

            # Try each selector
            for i, selector in enumerate(selectors, 1):
                try:
                    self.logger.debug(f"Trying selector {i}: {selector}")

                    if selector.startswith('//') or selector.startswith('/'):
                        # XPath selector
                        element = self._find_element_silent(selector, 'xpath', timeout=2)
                    else:
                        # CSS selector
                        element = self._find_element_silent(selector, 'css', timeout=2)

                    if element and element.is_displayed():
                        self.logger.info(f"Found element using selector {i}: {selector}")

                        # Click the element
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()

                        sleep(uniform(1.5, 3.0))
                        self.logger.info("HTML parsing: Button clicked successfully")
                        return True

                except Exception as e:
                    self.logger.debug(f"Selector {i} failed: {str(e)}")
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"Button clicking by attributes failed: {str(e)}")
            return False



    def _check_suspicious_activity_proactively(self):
        """Proactively navigate to notifications page to check for suspicious activity alerts"""
        try:
            current_url = self.browser.this_url()
            self.logger.info("Navigating to Google Account notifications page to check for alerts...")

            # Navigate to notifications page
            self.browser.go("https://myaccount.google.com/notifications")
            sleep(uniform(2.0, 3.0))

            # Simulate idle mouse movements during page loading
            if hasattr(self.browser, 'simulate_page_loading_activity'):
                self.browser.simulate_page_loading_activity()

            # Check for suspicious activity on notifications page
            suspicious_detected = self._detect_suspicious_activity()

            if not suspicious_detected:
                # If no suspicious activity found, mark as cleared and return to original page
                self._update_suspicious_activity_status(self.browser.email, True)
                self.logger.info("No suspicious activity found on notifications page - marked as cleared")

                # Return to original page if it was different
                if current_url != "https://myaccount.google.com/notifications":
                    self.browser.go(current_url)
                    sleep(uniform(1.0, 2.0))
            else:
                # If suspicious activity was found and handled, also mark as cleared
                self.logger.info("Suspicious activity was handled - marking as cleared")
                self._update_suspicious_activity_status(self.browser.email, True)

            return suspicious_detected

        except Exception as e:
            self.logger.error(f"Error during proactive suspicious activity check: {str(e)}")
            return False



    def _has_silent_detection(self):
        """Check if enhanced driver with silent detection is available"""
        return (self._enhanced_driver is not None and
                hasattr(self._enhanced_driver, 'find_xpath_silent') and
                hasattr(self._enhanced_driver, 'find_css_silent'))




    def _find_element_silent(self, selector, by_type='xpath'):
        """Find element using silent detection if available, otherwise use fallback without timeout errors"""
        try:
            if self._has_silent_detection():
                if by_type == 'xpath':
                    return self._enhanced_driver.find_xpath_silent(selector)
                elif by_type == 'css':
                    return self._enhanced_driver.find_css_silent(selector)
            else:
                # Fallback to regular find without wait (immediate check only)
                try:
                    if by_type == 'xpath':
                        return self.browser.find_xpath(selector)
                    elif by_type == 'css':
                        return self.browser.find_css(selector)
                except:
                    return None
        except Exception as e:
            self.logger.debug(f"Silent element detection failed for {selector}: {str(e)}")
            return None



    def terminate_selenium_driver(self):
        try:
            for process in psutil.process_iter(attrs=['pid', 'name', 'cmdline']):
                try:
                    process_info = process.info
                    # Add null check to prevent NoneType error
                    if process_info.get('name') and 'chromedriver' in process_info.get('name').lower():
                        self.logger.info(f"Terminating chromedriver process: {process_info['pid']}")
                        process.terminate()
                        process.wait(timeout=5)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue
        except Exception as e:
            self.logger.error(f"Error cleaning up chromedriver processes: {str(e)}")


    def remove_profile(self, email):
        try:
            profile = f"{profile_home}/{email}"
            if os.path.exists(profile):
                # Use shutil.rmtree to delete the directory and its contents
                shutil.rmtree(profile)
                self.logger.info(f"Profile {profile} removed successfully.")
            else:
                self.logger.warning(f"Profile {profile} does not exist.")
        except Exception as e:
            self.logger.error(f"Error removing profile {profile}: {str(e)}")



    def login_Wait(self):
        self.logger.warning("=== Infinite Wait Is Enable to Stop, please close browser ===")
        while self.browser.running() == True:
            sleep(0.5)


    def wait(self):
        while self.browser.running() == True:
            sleep(2.5)


    def wait_for_verification(self):
        while self.browser.running() == True and "signin/challenge/iap" in self.browser.this_url():
            sleep(2.5)



    def add_group(self, email, group):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"] == group:
                    g["members_num"] = 0
                    break
            else:
                data[email].append({
                    "name": group, 
                    "members_num": 0
                })
        else:
            data[email] = [{
                "name": group, 
                "members_num": 0
            }]

        with open(map_path, 'w') as f:
            json.dump(data, f, indent=4)



    
    def update_group_members(self, email, group, membrs_num):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"].lower() == group.lower():
                    g["members_num"] = str(membrs_num)
                    break
            else:
                self.logger.info(f"### Group {group} not found for email {email} ###")
        else:
            self.logger.info(f"### Email {email} not found ###")

        with open(map_path, 'w') as f:
            json.dump(data, f, indent=4)


    def get_groups_map(self,email):
        data = {}
        
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        return data.get(email, None)
    


    def get_groups(self,email):
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)
                groups = data.get(email, [])
                return [group['name'] for group in groups if 'name' in group]
        else:
            return None

    

    def remove_group(email, group):
        data = {}

        if os.path.exists("GroupsMap.json"):
            with open("GroupsMap.json", 'r') as f:
                data = json.load(f)

        if email in data and group in data[email]:
            data[email].remove(group)

            if not data[email]:
                del data[email]

            with open("GroupsMap.json", 'w') as f:
                json.dump(data, f)


    def update_email_status(self, email, status):
        with open(gmail_map_file, 'r') as f:
            data = json.load(f)

        for item in data:
            if item['email'] == email:
                item['status'] = status
                break

        with open(gmail_map_file, 'w') as f:
            json.dump(data, f, indent=4)


    def update_email_pass(self, email, password):
        with open(gmail_map_file, 'r') as f:
            data = json.load(f)

        for item in data:
            if item['email'] == email:
                item['password'] = password
                break

        with open(gmail_map_file, 'w') as f:
            json.dump(data, f, indent=4)


    def check_js(self, elem):
        """Optimized JavaScript text content checker"""
        var_js = f"return document.body.textContent.includes({elem})"
        try:
            found = self.browser.execute_js(var_js)
            # Handle both boolean and string returns
            return found is True or found == "True" or found == True
        except Exception as e:
            self.logger.debug(f"JavaScript check failed for {elem}: {str(e)}")
            return False


    def CaptchaVerif(self):
        for xpath in cp_xpaths:
            try:
                # Use silent detection if enhanced driver is available
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent(xpath)
                else:
                    # Fallback to regular find with try/catch
                    try:
                        element = self.browser.find_xpath(xpath)
                    except:
                        element = None

                if element:
                    self.logger.error(f"Captcha Detected with XPath: {xpath}")
                    return True
            except Exception as e:
                self.logger.debug(f"Error checking captcha xpath {xpath}: {str(e)}")
                continue
        return False
    

    def ImageCaptchaVerif(self):
        """Detect image CAPTCHAs specifically for Gmail login flow with enhanced validation"""
        for xpath in image_captcha_xpaths:
            try: 
                # Use silent detection if enhanced driver is available
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent(xpath)
                    print(element)
                else:
                    # Fallback to regular find with try/catch
                    try:
                        element = self.browser.find_xpath(xpath)
                    except:
                        element = None

                if element:
                    self.logger.debug(f"Found potential CAPTCHA element with XPath: {xpath}")

                    if self._validate_captcha_element(element, xpath):
                        self.logger.warning(f"Active Image CAPTCHA Detected with XPath: {xpath}")
                        return True
                    else:
                        self.logger.debug(f"CAPTCHA element found but not active/visible: {xpath}")

            except Exception as e:
                self.logger.debug(f"Error checking image captcha xpath {xpath}: {str(e)}")
                continue
        return False


    def _validate_captcha_element(self, element, xpath):
        """Validate that a CAPTCHA element is actually displayed and functional"""
        try:
            # Check 1: Element must be displayed/visible
            if not element.is_displayed():
                print(f"CAPTCHA element not displayed: {xpath}")
                #return False

            # Check 2: For image elements, verify src attribute exists and is not empty
            if xpath.endswith('//img') or '//img[' in xpath or xpath.endswith("'captchaimg']"):
                src = element.get_attribute('src')
                if not src or src.strip() == '':
                    self.logger.debug(f"CAPTCHA image has no src attribute: {xpath}")
                    return False

                # Check for placeholder or empty images
                if src in ['', 'about:blank', 'data:,']:
                    self.logger.debug(f"CAPTCHA image has placeholder src: {src}")
                    return False

                self.logger.debug(f"CAPTCHA image has valid src: {src[:50]}...")


            self.logger.debug(f"CAPTCHA element validation passed: {xpath}")
            return True

        except Exception as e:
            self.logger.debug(f"Error validating CAPTCHA element {xpath}: {str(e)}")
            return False



    def extract_image_captcha(self):
        """Extract image CAPTCHA from the page and save it for processing with enhanced validation"""
        try:
            # Try to find the CAPTCHA image element
            captcha_img = None
            found_xpath = None

            # Primary method: look for img#captchaimg
            try:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    captcha_img = self._enhanced_driver.find_xpath_silent("//img[@id='captchaimg']")
                else:
                    captcha_img = self.browser.find_xpath("//img[@id='captchaimg']")
                if captcha_img:
                    found_xpath = "//img[@id='captchaimg']"
            except:
                pass

            # Fallback methods if primary fails
            if not captcha_img:
                fallback_xpaths = [
                    "//div[contains(@jscontroller, 'CMcBD')]//img",
                    "//div[contains(@class, 'captcha')]//img",
                    "//img[contains(@src, 'captcha')]"
                ]

                for xpath in fallback_xpaths:
                    try:
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            captcha_img = self._enhanced_driver.find_xpath_silent(xpath)
                        else:
                            captcha_img = self.browser.find_xpath(xpath)
                        if captcha_img:
                            found_xpath = xpath
                            break
                    except:
                        continue

            if not captcha_img:
                self.logger.error("Could not find CAPTCHA image element")
                return None

            # Enhanced validation: verify the element is actually functional
            if not self._validate_captcha_element(captcha_img, found_xpath):
                self.logger.error("CAPTCHA image element found but not functional - failing fast")
                return None

            # Get the image source
            img_src = captcha_img.get_attribute('src')
            if not img_src or img_src.strip() == '':
                self.logger.error("CAPTCHA image has no src attribute - failing fast")
                return None

            # Additional validation for src content
            if img_src in ['', 'about:blank', 'data:,']:
                self.logger.error(f"CAPTCHA image has placeholder src: {img_src} - failing fast")
                return None

            self.logger.info(f"Found CAPTCHA image with src: {img_src[:100]}...")

            # Handle data URLs (base64 encoded images)
            if img_src.startswith('data:image'):
                # Extract base64 data
                try:
                    header, data = img_src.split(',', 1)
                    image_data = base64.b64decode(data)

                    # Save to temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                    temp_file.write(image_data)
                    temp_file.close()

                    self.logger.info(f"CAPTCHA image saved to: {temp_file.name}")
                    return temp_file.name

                except Exception as e:
                    self.logger.error(f"Error processing base64 image: {str(e)}")
                    return None

            # Handle regular URLs
            else:
                try:
                    # Download the image
                    response = requests.get(img_src, timeout=10)
                    response.raise_for_status()

                    # Save to temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                    temp_file.write(response.content)
                    temp_file.close()

                    self.logger.info(f"CAPTCHA image downloaded and saved to: {temp_file.name}")
                    return temp_file.name

                except Exception as e:
                    self.logger.error(f"Error downloading CAPTCHA image: {str(e)}")
                    return None

        except Exception as e:
            self.logger.error(f"Error extracting image CAPTCHA: {str(e)}")
            return None



    def solve_image_captcha(self, image_path):
        """Solve image CAPTCHA using anti-captcha service with updated API"""

        try:
            # Get configuration
            config = self.get_anticaptcha_config()
            api_key = config['api_key']

            # Initialize anti-captcha solver
            solver = imagecaptcha()
            solver.set_verbose(1)
            solver.set_key(api_key)

            # Check account balance
            try:
                balance = solver.get_balance()
                self.logger.info(f"Anti-captcha balance: ${balance}")

                if balance < 0.01:  # Minimum balance check
                    self.logger.error("Insufficient anti-captcha balance")
                    return None
            except Exception as e:
                self.logger.warning(f"Could not check anti-captcha balance: {str(e)}")

            self.logger.info("Submitting image CAPTCHA to anti-captcha service...")

            # Solve the captcha
            captcha_text = solver.solve_and_return_solution(image_path)

            if captcha_text and captcha_text != 0:
                # Validate the solution before returning
                is_valid, validation_msg = self.validate_captcha_solution(captcha_text)
                if is_valid:
                    self.logger.info(f"Image CAPTCHA solved: {captcha_text}")
                    return captcha_text
                else:
                    self.logger.error(f"Invalid CAPTCHA solution: {validation_msg}")
                    return None
            else:
                error_code = getattr(solver, 'error_code', 'Unknown error')
                self.logger.error(f"Anti-captcha failed: {error_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error solving image CAPTCHA with anti-captcha: {str(e)}")
            return None
        finally:
            # Clean up temporary image file
            try:
                if image_path and os.path.exists(image_path):
                    os.unlink(image_path)
                    self.logger.debug(f"Cleaned up temporary image file: {image_path}")
            except Exception as e:
                self.logger.warning(f"Could not clean up temporary file {image_path}: {str(e)}")



    def submit_image_captcha(self, captcha_solution):
        """Submit the solved image CAPTCHA to the form"""
        try:
            # Find the CAPTCHA input field
            captcha_input = None

            # Primary method: look for input[name="ca"]
            try:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    captcha_input = self._enhanced_driver.find_xpath_silent("//input[@name='ca']")
                else:
                    captcha_input = self.browser.find_xpath("//input[@name='ca']")
            except:
                pass

            # Fallback methods
            if not captcha_input:
                fallback_selectors = [
                    "//input[@id='ca']",
                    "//input[contains(@placeholder, 'captcha')]",
                    "//input[contains(@placeholder, 'text')]"
                ]

                for selector in fallback_selectors:
                    try:
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            captcha_input = self._enhanced_driver.find_xpath_silent(selector)
                        else:
                            captcha_input = self.browser.find_xpath(selector)
                        if captcha_input:
                            break
                    except:
                        continue

            if not captcha_input:
                self.logger.error("Could not find CAPTCHA input field")
                return False

            # Clear any existing text and enter the solution
            try:
                captcha_input.clear()
                sleep(0.5)

                # Use human-like typing if available
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(captcha_input, captcha_solution)
                else:
                    captcha_input.send_keys(captcha_solution)

                self.logger.info(f"Entered CAPTCHA solution: {captcha_solution}")
                sleep(1)

            except Exception as e:
                self.logger.error(f"Error entering CAPTCHA solution: {str(e)}")
                return False

            # Try to find and handle the hidden token field (ct)
            try:
                token_field = None
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    token_field = self._enhanced_driver.find_xpath_silent("//input[@name='ct']")
                else:
                    try:
                        token_field = self.browser.find_xpath("//input[@name='ct']")
                    except:
                        pass

                if token_field:
                    token_value = token_field.get_attribute('value')
                    self.logger.info(f"Found hidden token field with value: {token_value[:20]}...")
                else:
                    self.logger.info("No hidden token field found (this may be normal)")

            except Exception as e:
                self.logger.debug(f"Error checking token field: {str(e)}")

            # Submit the form
            try:
                # Try to find and click the submit button
                submit_button = None
                submit_selectors = [
                    "//button[contains(text(), 'Next')]",
                    "//button[contains(text(), 'Suivant')]",
                    "//input[@type='submit']",
                    "//button[@type='submit']",
                    "//div[@role='button'][contains(text(), 'Next')]",
                    "//div[@role='button'][contains(text(), 'Suivant')]"
                ]

                for selector in submit_selectors:
                    try:
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            submit_button = self._enhanced_driver.find_xpath_silent(selector)
                        else:
                            try:
                                submit_button = self.browser.find_xpath(selector)
                            except:
                                pass
                        if submit_button:
                            break
                    except:
                        continue

                if submit_button:
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(submit_button)
                    else:
                        submit_button.click()
                    self.logger.info("Clicked submit button after entering CAPTCHA")
                else:
                    # Try submitting with Enter key
                    captcha_input.send_keys(Keys.ENTER)
                    self.logger.info("Submitted CAPTCHA with Enter key")

                sleep(2)
                return True

            except Exception as e:
                self.logger.error(f"Error submitting CAPTCHA form: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"Error in submit_image_captcha: {str(e)}")
            return False



    def ImageCaptchaSolver(self, max_retries=3, retry_delay=5):
        """Complete image CAPTCHA solving workflow with enhanced validation and retry mechanism"""

        # Pre-validation: Double-check that CAPTCHA is actually required
        if not self.ImageCaptchaVerif():
            self.logger.info("Image CAPTCHA verification failed on re-check - no active CAPTCHA found")
            return True  # Not an error, just no CAPTCHA to solve

        for attempt in range(max_retries):
            try:
                self.logger.warning(f"### Starting Image CAPTCHA solving process (Attempt {attempt + 1}/{max_retries}) ###")

                # Step 1: Extract the CAPTCHA image with enhanced validation
                image_path = self.extract_image_captcha()
                if not image_path:
                    # Check if this is a validation failure (element exists but not functional)
                    # vs a genuine missing element
                    if self._is_captcha_element_present_but_invalid():
                        self.logger.warning("CAPTCHA element present but not functional - skipping retries")
                        return True  # Don't retry for false positives

                    self.logger.error(f"Failed to extract CAPTCHA image (Attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        sleep(retry_delay)
                        continue
                    return False

                # Step 2: Solve the CAPTCHA using anti-captcha service
                captcha_solution = self.solve_image_captcha(image_path)
                if not captcha_solution:
                    self.logger.error(f"Failed to solve image CAPTCHA (Attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        sleep(retry_delay)
                        continue
                    return False

                # Step 3: Submit the solution
                success = self.submit_image_captcha(captcha_solution)
                if success:
                    self.logger.info("### Image CAPTCHA solved and submitted successfully! ###")
                    return True
                else:
                    self.logger.error(f"Failed to submit image CAPTCHA solution (Attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        sleep(retry_delay)
                        continue
                    return False

            except Exception as e:
                self.logger.error(f"Error in ImageCaptchaSolver (Attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    self.logger.info(f"Retrying in {retry_delay} seconds...")
                    sleep(retry_delay)
                    continue
                return False

        self.logger.error(f"Image CAPTCHA solving failed after {max_retries} attempts")
        return False



    def _is_captcha_element_present_but_invalid(self):
        """Check if CAPTCHA elements exist in DOM but are not functional (false positive detection)"""
        try:
            for xpath in image_captcha_xpaths:
                try:
                    # Find element without validation
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(xpath)
                    else:
                        try:
                            element = self.browser.find_xpath(xpath)
                        except:
                            element = None

                    if element:
                        # Element exists, but check if it's invalid
                        if not self._validate_captcha_element(element, xpath):
                            self.logger.debug(f"Found invalid CAPTCHA element: {xpath}")
                            return True

                except Exception as e:
                    self.logger.debug(f"Error checking element presence {xpath}: {str(e)}")
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"Error in _is_captcha_element_present_but_invalid: {str(e)}")
            return False



    def get_anticaptcha_config(self):
        """Get anti-captcha configuration from settings or use defaults"""
        try:
            # Try to load from enhanced settings first
            if os.path.exists(f"{home}/json/enhanced_settings.json"):
                with open(f"{home}/json/enhanced_settings.json", 'r') as f:
                    settings = json.load(f)
                    captcha_config = settings.get('captcha', {})

                    return {
                        'api_key': captcha_config.get('api_key'),
                        'max_solve_time': captcha_config.get('max_solve_time', 120),
                        'auto_retry': captcha_config.get('auto_retry', True),
                        'service': captcha_config.get('service', 'anticaptcha')
                    }
        except Exception as e:
            self.logger.debug(f"Could not load captcha config from settings: {str(e)}")

        # Return default configuration
        return {
            'api_key': "d315b270071ccc3922a75b7c56e72da1",
            'max_solve_time': 120,
            'auto_retry': True,
            'service': 'anticaptcha'
        }



    def validate_captcha_solution(self, solution):
        """Validate CAPTCHA solution before submission"""
        if not solution:
            return False, "Empty solution"

        # Basic validation rules
        if len(solution) < 3:
            return False, "Solution too short"

        if len(solution) > 10:
            return False, "Solution too long"

        # Check for suspicious patterns
        if solution.lower() in ['error', 'failed', 'timeout']:
            return False, "Invalid solution pattern"

        return True, "Valid solution"


    def solve_captcha(self,url):
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        solver = TwoCaptcha(api_key)
        if "signin" in url:
            custom_site_key = "6LdD2OMZAAAAAAv2xVpeCk8yMtBtY3EhDWldrBbh"
        else:
            custom_site_key = "6LctgAgUAAAAACsC7CsLr_jgOWQ2ul2vC_ndi8o2"
        try:
            result = solver.recaptcha(
                sitekey=custom_site_key,
                #url='https://groups.google.com/my-groups?hl=fr-FR'
                url=url
                )
            print(f"This is the result of captcha submission : {result}")

        except Exception as e:
            self.logger.error(f"{str(e)}")

        else:
            #print('solved: ' + str(result))
            #print("Captcha Solved!!")
            return result['code']

   
    def CaptchaSolver(self,url):
        # Simulate human-like analysis of CAPTCHA before solving
        if hasattr(self.browser, 'simulate_captcha_analysis'):
            self.browser.simulate_captcha_analysis(duration=uniform(2.0, 4.0))

        token = self.solve_captcha(url)

        # Try to get callback if method exists, otherwise use fallback approach
        try:
            if hasattr(self.browser, 'get_callback'):
                print("Browser Has a Get_callback func!!")
                self.callback = self.browser.get_callback()
            else:
                # Fallback: try to find callback in JavaScript
                self.callback = self.browser.execute_js("""
                    // Try to find reCAPTCHA callback function
                    if (window.___grecaptcha_cfg && window.___grecaptcha_cfg.clients) {
                        for (let client in window.___grecaptcha_cfg.clients) {
                            let callbacks = window.___grecaptcha_cfg.clients[client].callback;
                            if (callbacks) return callbacks;
                        }
                    }
                    return null;
                """)
        except:
            self.callback = None

        try:
            cap_id = self.browser.execute_js(""" return document.querySelector('[name="g-recaptcha-response"]').id """)
        except:
            cap_id = None

        if self.callback is not None and cap_id is not None:
            try:
                self.browser.execute_js(f"""document.querySelector('#{cap_id}').innerText='{token}'; {self.callback}.call()""")
                self.logger.info("### captcha Submited!! ###")
            except:
                self.logger.error(f"+++ Can't Solve Captcha!! +++")
        else:
            # Alternative approach: try to submit the token directly
            try:
                self.browser.execute_js(f"""
                    document.querySelector('[name="g-recaptcha-response"]').innerText='{token}';
                    document.querySelector('[name="g-recaptcha-response"]').style.display = 'block';
                    // Try to trigger form submission or callback
                    if (window.grecaptcha && window.grecaptcha.getResponse) {{
                        window.grecaptcha.getResponse = function() {{ return '{token}'; }};
                    }}
                """)
                self.logger.info("### captcha token set with fallback method ###")
            except Exception as e:
                self.logger.error(f"+++ Fallback captcha method failed: {str(e)} +++")



    def passEmail(self):
        email_field = self.browser.wait_xpath_presence('//input[@type="email"]')
        try:
            # Use enhanced driver's human-like typing if available
            if hasattr(self.browser, 'human_type_text'):
                self.browser.human_type_text(email_field, self.browser.email)
            else:
                email_field.send_keys(self.browser.email)
        except:
            try:
                element = self.browser.find_xpath("//input[@id='identifierId']")
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(element, self.browser.email)
                else:
                    element.send_keys(self.browser.email)
            except:
                try:
                    element = self.browser.find_css('#identifierId')
                    if hasattr(self.browser, 'human_type_text'):
                        self.browser.human_type_text(element, self.browser.email)
                    else:
                        element.send_keys(self.browser.email)
                except:
                    try:
                        self.browser.execute_js(f'document.getElementsByName("identifier")[0].value = "{self.browser.email}"')
                    except:
                        pass

        try:
            button = self.browser.find_css('#identifierNext > div > button')
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(button)
            else:
                button.click()
        except:
            try:
                button = self.browser.find_xpath("//button[contains(., 'Suivant')]")
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(button)
                else:
                    button.click()
            except:
                try:
                    button = self.browser.find_xpath('//*[@id="identifierNext"]/div/button')
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(button)
                    else:
                        button.click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[id="identifierNext"]').click() """)
                    except:
                        self.browser.find_xpath('//input[@type="email"]').send_keys(Keys.ENTER)


    def passPassword(self):
        password_field = self.browser.wait_css_clickable("""input[type='password']""")
        try:
            # Use enhanced driver's human-like typing if available
            if hasattr(self.browser, 'human_type_text'):
                self.browser.human_type_text(password_field, self.browser.password)
            else:
                password_field.send_keys(self.browser.password)
        except:
            try:
                element = self.browser.find_css('#password > div.aCsJod.oJeWuf > div > div.Xb9hP > input')
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(element, self.browser.password)
                else:
                    element.send_keys(self.browser.password)
            except:
                try:
                    element = self.browser.find_xpath('//input[@aria-label="Saisissez votre mot de passe"]')
                    if hasattr(self.browser, 'human_type_text'):
                        self.browser.human_type_text(element, self.browser.password)
                    else:
                        element.send_keys(self.browser.password)
                except:
                    try:
                        self.browser.execute_js(f'document.getElementsByName("Passwd")[0].value = "{self.browser.password}"')
                    except:
                        pass

        sleep(0.8)

        try:
            button = self.browser.find_xpath('//*[@id="passwordNext"]/div/button')
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(button)
            else:
                button.click()
        except:
            try:
                button = self.browser.find_css('#passwordNext > div > button')
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(button)
                else:
                    button.click()
            except:
                try:
                    self.browser.execute_js(""" document.querySelector('[id="passwordNext"]').click() """)
                except:
                    password_field.send_keys(Keys.ENTER)



    def signchooser(self):
        try:
            self.browser.find_xpath('//*[@id="view_container"]/div/div/div[2]/div/div[1]/div/form/span/section/div/div/div/div/ul/li[1]/div').click()
        except:
            self.browser.find_css('#view_container > div > div > div.pwWryf.bxPAYd > div > div.WEQkZc > div > form > span > section > div > div > div > div > ul > li.JDAKTe.ibdqA.W7Aapd.zpCp3.SmR8 > div').click()
        sleep(1.3)
        
        self.passPassword()

    


    def webreauth(self):
        try:
            try:
                self.browser.find_css('#identifierNext > div > button > span').click()
            except:
                try:
                    self.browser.find_xpath("//button[@type='button' and  contains(., 'Suivant') ]").click()
                except:
                    try:
                        self.browser.find_xpath('//*[@id="identifierNext"]/div/button').click()
                    except:
                        raise RuntimeError("Can't Find Next Button!!")
            
            sleep(3)
            
            self.passPassword()
        
        except Exception as e:
            self.logger.error(f"{str(e)}")
            self.terminate_selenium_driver()


    def rejected(self):
        try:
            try:
                self.browser.find_xpath('//*[@id="accountRecoveryButton"]').click()
            except:
                try:
                    self.browser.find_css('#accountRecoveryButton').click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[aria-label="Continuer"]').click() """)
                    except:
                        raise RuntimeError("Can't Find Recovery Button!!")
        
            sleep(2.5)
            
            try:
                self.browser.find_xpath('//*[@id="identifierNext"]/div/button').click()
            except:
                try:
                    self.browser.find_css('#identifierNext > div > button').click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[type="button"]').click() """)
                    except:
                        raise RuntimeError("Can't Find Next Button!!")
                        
            sleep(2.2)

            self.passPassword()
            
            sleep(2.5)

            try:
                self.browser.find_xpath('//*[@id="knowledgePreregisteredEmailInput"]').send_keys(self.conf)
            except:
                try:
                    self.browser.find_css('#knowledgePreregisteredEmailInput').send_keys(self.conf)
                except:
                    try:
                        self.browser.execute_js(f""" document.querySelector('[type="email"]').value = "{self.conf}" """)
                    except:
                        raise RuntimeError("Can't Find Email Input!!")
        except Exception as e:
            self.logger.error(f"{str(e)}")
            self.terminate_selenium_driver()



    def handle_passkey(self):
        """Handle passkey prompt by clicking 'Not now' button"""
        try:
            self.logger.info("Attempting to handle passkey prompt...")

            # Try XPath selectors
            xpaths = [
                "//button[contains(@class, 'VfPpkd-LgbsSe') and .//span[text()='Not now']]",
                "//button[contains(@class, 'VfPpkd-LgbsSe') and contains(@class, 'ksBjEc') and contains(@class, 'lKxP2d')]",
                "//button[.//span[text()='Not now']]"
            ]

            for xpath in xpaths:
                try:
                    button = (self._enhanced_driver.find_xpath_silent(xpath)
                             if hasattr(self, '_enhanced_driver') and hasattr(self._enhanced_driver, 'find_xpath_silent')
                             else self.browser.find_xpath(xpath))

                    if button and button.is_displayed():
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(1.0, 2.0))
                        self.logger.info("Passkey prompt skipped successfully")
                        return True
                except:
                    continue

            # JavaScript fallback
            try:
                self.browser.execute_js("[...document.querySelectorAll('button')].find(btn => btn.textContent.includes('Not now'))?.click();")
                sleep(uniform(1.0, 2.0))
                self.logger.info("Passkey prompt skipped successfully via JavaScript")
                return True
            except:
                pass

            self.logger.warning("Could not find or click 'Not now' button")
            return False

        except Exception as e:
            self.logger.error(f"Error handling passkey prompt: {str(e)}")
            return False



    def phoneVerif(self):
        self.logger.info(f"Checking Phone Recovery!!")
        phone_recov = False

        try:
            # First, check if we're on a verification page that offers email confirmation
            if self._detect_email_confirmation_option():
                self.logger.info("Email confirmation option detected - prioritizing over phone verification")
                if self._handle_email_confirmation():
                   self.logger.info("Email confirmation handled")
                else:
                    self.logger.warning("Email confirmation failed, falling back to phone verification")

            # Optimized phone recovery detection - prioritized patterns for faster detection
            # Most common patterns first to reduce average detection time
            priority_patterns = [
                "//input[@type='tel']",                                   # Phone input field (most reliable)
                "//input[@name='phoneNumber']",                           # Phone number input
                "//input[@id='phoneNumberId']",                           # Google's phone input ID
            ]

            # Check priority patterns first for fastest detection
            for pattern in priority_patterns:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(pattern)
                    else:
                        try:
                            element = self.browser.find_xpath(pattern)
                        except:
                            element = None

                    if element:
                        self.logger.info(f"Phone Recovery Detected via priority pattern: {pattern}")
                        phone_recov = True
                        break
                except Exception as e:
                    self.logger.debug(f"Error checking priority pattern {pattern}: {str(e)}")
                    continue

            # If not found via priority patterns, check text-based patterns
            if not phone_recov:
                text_patterns = [
                    "//*[contains(text(),'Get a verification code')]",        # English
                    "//*[contains(text(),'Obtenir un code de validation')]",  # French
                    "//*[contains(text(),'phone number')]",                   # English phone number
                    "//*[contains(text(),'numéro de téléphone')]",            # French phone number
                    "//*[contains(text(),'Verify it')]",                      # English verification
                    "//*[contains(text(),'Confirmez qu')]",                    # French confirmation
                ]

                for pattern in text_patterns:
                    try:
                        if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                            element = self._enhanced_driver.find_xpath_silent(pattern)
                        else:
                            try:
                                element = self.browser.find_xpath(pattern)
                            except:
                                element = None

                        if element:
                            self.logger.info(f"Phone Recovery Detected via text pattern: {pattern}")
                            phone_recov = True
                            break
                    except Exception as e:
                        self.logger.debug(f"Error checking text pattern {pattern}: {str(e)}")
                        continue

        except Exception as e:
            self.logger.error(f"Error in phone recovery detection: {str(e)}")

        # Also check URL patterns
        current_url = self.browser.this_url()
        url_patterns = [
            "signin/challenge/iap",
            "signin/challenge/ipp",
            "signin/v3/challenge/selection",
            "accounts.google.com/signin/v3/challenge"
        ]

        url_detected = any(pattern in current_url for pattern in url_patterns)
        if url_detected:
            self.logger.info(f"Phone Recovery Detected via URL: {current_url}")
            phone_recov = True

        # Check page content with JavaScript only if not already detected
        if not phone_recov:
            try:
                # Optimized: Check all indicators in a single JavaScript execution
                page_content_indicators = [
                    "Enter a phone number",  # Most common English
                    "Saisissez un numéro de téléphone",  # Most common French
                    "Verify it's you",
                    "Confirmez qu'il s'agit bien de vous",
                    "verification code",
                    "code de validation"
                ]

                # Single JavaScript call to check all indicators at once
                indicators_js = " || ".join([f'document.body.textContent.includes("{indicator}")' for indicator in page_content_indicators])
                combined_js = f"return {indicators_js}"

                try:
                    found = self.browser.execute_js(combined_js)
                    if found:
                        # If found, determine which specific indicator matched (for logging)
                        for indicator in page_content_indicators:
                            if self.check_js(f'"{indicator}"'):
                                self.logger.info(f"Phone Recovery Detected via page content: {indicator}")
                                phone_recov = True
                                break
                except Exception as js_error:
                    self.logger.debug(f"Combined JavaScript check failed, trying individual checks: {str(js_error)}")
                    # Fallback to individual checks if combined fails
                    for indicator in page_content_indicators[:2]:  # Only check most common ones
                        try:
                            if self.check_js(f'"{indicator}"'):
                                self.logger.info(f"Phone Recovery Detected via page content: {indicator}")
                                phone_recov = True
                                break
                        except:
                            continue

            except Exception as e:
                self.logger.error(f"Error checking page content: {str(e)}")

        if phone_recov:
            self.logger.warning(f"### PHONE RECOVERY REQUIRED ###")
            self.update_email_status(self.browser.email, "phone_verification_required")

            # Try to handle phone verification automatically
            if hasattr(self, '_handle_phone_verification'):
                self.logger.info("Attempting automatic phone verification handling...")
                self._handle_phone_verification()
            else:
                self.logger.info("Waiting for manual phone verification...")
                self.wait_for_verification()
        else:
            # Try to click "Don't ask again on this device" if available
            try:
                dont_ask_patterns = [
                    "//*[contains(text(),'Ne plus me demander sur cet appareil')]",  # French
                    "//*[contains(text(),'Don\\'t ask again on this device')]",      # English
                    "//*[contains(text(),'Remember this device')]",                 # Alternative English
                    "//*[contains(text(),'Se souvenir de cet appareil')]"           # Alternative French
                ]

                for pattern in dont_ask_patterns:
                    try:
                        element = self.browser.find_xpath(pattern)
                        if element:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(element)
                            else:
                                element.click()
                            self.logger.info(f"Clicked 'Don't ask again' button: {pattern}")
                            sleep(uniform(1.0, 2.0))
                            break
                    except:
                        continue

            except Exception as e:
                self.logger.error(f"Error clicking 'Don't ask again': {str(e)}")

        self.logger.info(f"Phone Recovery >> {phone_recov}")
        return phone_recov



    def change_password(self):
        self.new_pass = f"@{self.browser.password}@"

        try:
            self.browser.wait_xpath_presence('//*[@id="passwd"]/div[1]/div/div[1]/input')
        except:
            self.browser.wait_xpath_presence('//*[@id="Password"]')
        try:
            self.browser.find_xpath('//*[@id="passwd"]/div[1]/div/div[1]/input').send_keys(self.new_pass)
        except:
            self.browser.find_xpath('//*[@id="Password"]').send_keys(self.new_pass)
        sleep(0.5)
        try:
            self.browser.find_xpath('//*[@id="confirm-passwd"]/div[1]/div/div[1]/input').send_keys(self.new_pass)
            self.browser.find_xpath('//*[@id="confirm-passwd"]/div[1]/div/div[1]/input').send_keys(Keys.ENTER)
        except:
            self.browser.find_xpath('//*[@id="ConfirmPassword"]').send_keys(self.new_pass)
            self.browser.find_xpath('//*[@id="ConfirmPassword"]').send_keys(Keys.ENTER)
        
        self.update_email_pass(self.browser.email, self.new_pass)
        self.logger.info(f"### Password Changed!! ###")


    def login(self):
        #### &hl=fr-FR ####
        #sleep(3)

        # Check if account is in cooldown before attempting login
        if hasattr(self, 'browser') and self.browser and hasattr(self.browser, 'email'):
            if self._should_skip_account_due_to_cooldown(self.browser.email):
                return  # Skip this account due to cooldown

        self.passEmail()

        sleep(0.7)

        # ENHANCED: Check for reCAPTCHA after email submission (CRITICAL FIX)
        if self._detect_recaptcha_after_email():
            self.logger.warning("reCAPTCHA detected after email submission - handling automatically")
            if not self._handle_recaptcha_challenge():
                self.logger.error("Failed to handle reCAPTCHA challenge - login may fail")
                # Don't return here - continue with manual intervention possibility


        # Check for image CAPTCHA (Gmail login specific)
        if self.ImageCaptchaVerif():
            self.logger.warning("Image CAPTCHA detected in Gmail login flow")
            if not self.ImageCaptchaSolver():
                self.logger.error("Failed to solve image CAPTCHA - login may fail")

        sleep(2)

        # ENHANCED: Check for reCAPTCHA after email submission (CRITICAL FIX)
        if self._detect_recaptcha_after_email():
            self.logger.warning("reCAPTCHA detected after email submission - handling automatically")
            if not self._handle_recaptcha_challenge():
                self.logger.error("Failed to handle reCAPTCHA challenge - login may fail")
                # Don't return here - continue with manual intervention possibility

        # ENHANCED: Safe password entry with reCAPTCHA fallback
        if not self._safe_password_entry():
            self.logger.error("Password entry failed - may be due to unhandled reCAPTCHA")
            return

        sleep(2)

        # Check for image CAPTCHA after password entry as well
        if self.ImageCaptchaVerif():
            self.logger.warning("Image CAPTCHA detected in Gmail login flow")
            if not self.ImageCaptchaSolver():
                self.logger.error("Failed to solve image CAPTCHA - login may fail")

        sleep(0.5)

        if "passkey" in self.browser.this_url():
            self.logger.info("Passkey detected - handling automatically")
            self.handle_passkey()

        

        if "https://myaccount.google.com" not in self.browser.this_url():
            self.phoneVerif()

        sleep(1.5)

        if"speedbump/changepassword" in self.browser.this_url():
            self.change_password()

        # IMPROVED POST-LOGIN SEQUENCE
        # Following the memory guidance: "Language change to French should be performed first in Gmail login flow, before checking for suspicious activity popups"
        # and "should directly visit https://myaccount.google.com/notifications to proactively clear any suspicious activity alerts"

        try:
            self.logger.info("Starting improved post-login sequence...")

            # Step 1: Confirm login success
            self.logger.info("Step 1: Confirming login success...")
            login_success = self._confirm_login_success()

            if login_success:
                # Step 2: Change language to French FIRST (as per memory guidance)
                if self._should_change_language_to_french():
                    self.logger.info("Step 2: Changing account language to French (first priority)")
                    self._change_gmail_language_to_french()
                    # Note: Suspicious activity clearing is now integrated into _change_gmail_language_to_french()
                else:
                    self.logger.info("Step 2: Language already changed to French - skipping")

                    # If language change was skipped, still check for suspicious activity independently
                    self.logger.info("Step 3: Checking for suspicious activity (language change was skipped)")
                    if not self._is_suspicious_activity_cleared(self.browser.email):
                        if self._handle_suspicious_activity():
                            self.logger.info("Security challenges detected and handled")
                    else:
                        self.logger.info("Suspicious activity already cleared - skipping")

                self.logger.info("Improved post-login sequence completed successfully")
            else:
                self.logger.warning("Login success not confirmed - skipping post-login sequence")

        except Exception as e:
            self.logger.error(f"Error in improved post-login sequence: {str(e)}")



    def _detect_recaptcha_after_email(self):
        """
        Detect reCAPTCHA challenge after email submission
        Checks both URL patterns and page elements
        """
        try:
            current_url = self.browser.this_url()

            # Check URL patterns for reCAPTCHA
            recaptcha_url_patterns = [
                "challenge/recaptcha",
                "signin/challenge/recaptcha",
                "accounts.google.com/signin/v3/challenge/recaptcha",
                "recaptcha/api2",
                "recaptcha/enterprise"
            ]

            url_detected = any(pattern in current_url for pattern in recaptcha_url_patterns)
            if url_detected:
                self.logger.warning(f"reCAPTCHA detected via URL pattern: {current_url}")
                return True

            try:
                if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                    element = self._enhanced_driver.find_xpath_silent("//iframe[@title='reCAPTCHA']")
                else:
                    try:
                        element = self.browser.find_xpath("//iframe[@title='reCAPTCHA']")
                    except:
                        element = None

                if element and element.is_displayed():
                    self.logger.warning(f"reCAPTCHA detected via element: //iframe[@title='reCAPTCHA']")
                    return True

            except Exception as e:
                self.logger.debug(f"Error checking reCAPTCHA selector //iframe[@title='reCAPTCHA']: {str(e)}")

            """ 
            try:
                page_content = self.browser.execute_js("return document.body.textContent || document.body.innerText || '';")
                recaptcha_text_indicators = [
                    "I'm not a robot",
                    "Je ne suis pas un robot",
                    "reCAPTCHA",
                    "Please complete the security check",
                    "Veuillez compléter la vérification de sécurité"
                ]

                for indicator in recaptcha_text_indicators:
                    if indicator.lower() in page_content.lower():
                        self.logger.warning(f"reCAPTCHA detected via text indicator: {indicator}")
                        return True

            except Exception as e:
                self.logger.debug(f"Error checking page content for reCAPTCHA: {str(e)}")
            """

            return False

        except Exception as e:
            self.logger.error(f"Error detecting reCAPTCHA after email: {str(e)}")
            return False


    def _handle_recaptcha_challenge(self):
        """
        Handle reCAPTCHA challenge using 2captcha service
        Returns True if handled successfully, False otherwise
        """
        try:
            self.logger.info("Attempting to handle reCAPTCHA challenge automatically...")

            try:
                self.logger.info(self.browser.this_url())
                try:
                    self.CaptchaSolver(self.browser.this_url())
                except Exception as e:
                    print(str(e))
                sleep(1)  # Wait for solution to be processed

                # Check if reCAPTCHA was solved successfully
                if not self._detect_recaptcha_after_email():
                    self.logger.info("reCAPTCHA solved successfully")
                    return True
                else:
                    self.logger.warning("reCAPTCHA solution may have failed - still detected")

            except Exception as e:
                self.logger.error(f"Error in automatic reCAPTCHA solving: {str(e)}")

            # If automatic solving failed, prepare for manual intervention
            self.logger.warning("Automatic reCAPTCHA solving failed - manual intervention may be required")
            self.logger.info("Browser will remain open for manual reCAPTCHA solving")

            # Wait a reasonable time for manual solving
            manual_solve_timeout = 240  # 4 minutes
            start_time = time.time()

            while time.time() - start_time < manual_solve_timeout:
                sleep(5)  # Check every 5 seconds

                # Check if reCAPTCHA is still present
                if not self._detect_recaptcha_after_email():
                    self.logger.info("reCAPTCHA appears to be solved (manual intervention)")
                    return True

                # Check if we've moved to password page
                try:
                    password_field = self._find_element_silent("input[type='password']", 'css', timeout=1)
                    if password_field:
                        self.logger.info("Password field detected - reCAPTCHA likely solved")
                        return True
                except:
                    pass

            self.logger.warning(f"Manual reCAPTCHA solving timeout after {manual_solve_timeout} seconds")
            return False

        except Exception as e:
            self.logger.error(f"Error handling reCAPTCHA challenge: {str(e)}")
            return False



    def _safe_password_entry(self):
        """
        Safe password entry with reCAPTCHA detection and timeout handling
        Returns True if successful, False if failed
        """
        try:
            # First check if we're still on reCAPTCHA page
            if self._detect_recaptcha_after_email():
                self.logger.warning("Still on reCAPTCHA page - cannot proceed to password entry")
                self.logger.info("Manual reCAPTCHA solving required before password entry")
                return False

            # Try to find password field with extended timeout for manual reCAPTCHA solving
            try:
                self.logger.info("Waiting for password field (extended timeout for reCAPTCHA handling)...")
                password_field = self.browser.wait_css_clickable("input[type='password']", timeout=240)  # Extended from 25 to 60 seconds

                # Proceed with normal password entry
                self.passPassword()
                return True

            except TimeoutException:
                self.logger.error("Timeout waiting for password field - likely due to unresolved reCAPTCHA")

                # Check if we're still on reCAPTCHA page
                if self._detect_recaptcha_after_email():
                    self.logger.error("Confirmed: Still on reCAPTCHA page after timeout")
                    self.logger.info("Browser will remain open for manual reCAPTCHA solving")
                    return False
                else:
                    self.logger.error("Not on reCAPTCHA page but password field not found - unknown state")
                    return False

        except Exception as e:
            self.logger.error(f"Error in safe password entry: {str(e)}")
            return False



    def _confirm_login_success(self):
        """Confirm that login was successful by checking URL and page indicators"""
        try:
            current_url = self.browser.this_url()
            self.logger.info(f"Confirming login success - current URL: {current_url}")

            # Check for successful login indicators
            success_indicators = [
                "myaccount.google.com",
                "accounts.google.com/signin/v2/challenge/selection",
                "mail.google.com",
                "gmail.com"
            ]

            # Check URL indicators
            for indicator in success_indicators:
                if indicator in current_url:
                    self.logger.info(f"Login success confirmed via URL: {indicator}")
                    return True

            # Check for page elements that indicate successful login
            success_elements = [
                '//div[contains(@class, "gb_")]',  # Google bar
                '//a[contains(@href, "myaccount.google.com")]',  # Account link
                '//div[contains(@class, "account")]',  # Account elements
                '//button[contains(@aria-label, "Google Account")]'  # Account button
            ]

            for element_xpath in success_elements:
                try:
                    element = self.browser.find_xpath(element_xpath)
                    if element and element.is_displayed():
                        self.logger.info(f"Login success confirmed via element: {element_xpath}")
                        return True
                except:
                    continue

            # If we can't confirm success, assume it's successful to continue the flow
            self.logger.info("Could not definitively confirm login success, but continuing...")
            return True

        except Exception as e:
            self.logger.error(f"Error confirming login success: {str(e)}")
            return True  # Default to success to continue flow



    def perform_human_actions_after_login(self):
        """
        Perform realistic human actions after successful Gmail login to warm up the account
        and establish natural browsing patterns. This helps avoid detection and makes the
        account appear more legitimate.
        """
        self.logger.info("### Starting Human Actions After Login ###")

        try:
            # IMPROVED SEQUENCE: Use the same approach as in login() method
            # Language change and security alert clearing should be handled by the main login flow
            # This method now focuses on human actions after the improved post-login sequence


            try:
                # Check if language change and security clearing were already handled in login()
                if self._should_change_language_to_french():
                    self.logger.info("Language change not yet completed - performing during human actions")
                    self._change_gmail_language_to_french()
                else:
                    self.logger.info("Language change already completed - proceeding with human actions")
            except Exception as e:
                self.logger.error(f"Error in language/security handling during human actions: {str(e)}")

            # SECOND PRIORITY: Check for phone verification or other security challenges
            if self._handle_suspicious_activity():
                self.logger.info("Security challenges detected and handled")
                return  # Exit human actions if phone verification is required

            if not self._is_2fa_enabled(self.browser.email):
                self._enable_2fa_automatically()
                self.logger.info("2fa enabled successfully")
            else:
                self.logger.info("2fa already enabled for this account - skipping 2fa setup")

            # Update password if not already updated
            if not self._is_password_updated(self.browser.email):
                self._update_password_status(self.browser.email)
                self.logger.info("Password updated successfully")
            else:
                self.logger.info("Password already updated for this account - skipping password update")

            sleep(uniform(2.0, 4.0))

            # Simulate idle movements during wait
            if hasattr(self.browser, 'simulate_page_loading_activity'):
                self.browser.simulate_page_loading_activity()

            self.browser.go("https://myaccount.google.com")

            sleep(uniform(2.0, 4.0))

            # Simulate idle movements during page loading
            if hasattr(self.browser, 'simulate_page_loading_activity'):
                self.browser.simulate_page_loading_activity()


            self.logger.info("### Human Actions After Login Completed Successfully ###")

        except Exception as e:
            self.logger.error(f"Error during human actions: {str(e)}")
            # Continue with the main flow even if human actions fail
            pass



    def _find_element_silent(self, selector, by_type='xpath', timeout=5):
        """
        Find element without logging errors for failed attempts
        Used during sync button detection to avoid spam logs
        """
        try:
            if by_type.lower() == 'xpath':
                element = WebDriverWait(self.browser, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
            elif by_type.lower() == 'css':
                element = WebDriverWait(self.browser, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
            else:
                return None

            return element if element.is_displayed() else None

        except:
            return None



    def _get_element_text_content(self, element):
        """
        Get text content from element, handling Chrome custom elements and shadow DOM

        Args:
            element: WebElement to extract text from

        Returns:
            str: Text content of the element
        """
        try:
            # Try standard text property first
            if hasattr(element, 'text') and element.text.strip():
                return element.text.strip()

            # Try innerHTML for custom elements
            try:
                inner_html = element.get_attribute('innerHTML')
                if inner_html:
                    # Remove HTML tags to get plain text
                    import re
                    text = re.sub(r'<[^>]+>', '', inner_html).strip()
                    if text:
                        return text
            except:
                pass

            # Try textContent attribute
            try:
                text_content = element.get_attribute('textContent')
                if text_content and text_content.strip():
                    return text_content.strip()
            except:
                pass

            # Try innerText attribute
            try:
                inner_text = element.get_attribute('innerText')
                if inner_text and inner_text.strip():
                    return inner_text.strip()
            except:
                pass

            # For cr-button elements, try to access shadow DOM content
            try:
                if element.tag_name.lower() == 'cr-button':
                    # Execute JavaScript to get shadow DOM text content
                    script = """
                    var element = arguments[0];
                    if (element.shadowRoot) {
                        return element.shadowRoot.textContent || element.shadowRoot.innerText || '';
                    }
                    return element.textContent || element.innerText || '';
                    """
                    shadow_text = self.browser.execute_script(script, element)
                    if shadow_text and shadow_text.strip():
                        return shadow_text.strip()
            except:
                pass

            return 'N/A'

        except Exception as e:
            self.logger.debug(f"Error getting element text content: {str(e)}")
            return 'N/A'



    def _handle_post_login_security_challenges(self):
        """
        Handle various security challenges that may appear after login
        Returns True if a security challenge was detected (human actions should stop)
        Returns False if no challenges detected (continue with human actions)
        """
        self.logger.info("Checking for post-login security challenges...")

        try:
            current_url = self.browser.this_url()

            # ALWAYS check for suspicious activity first - it can appear on any page
            # Also proactively check the notifications page if not already cleared
            if not self._is_suspicious_activity_cleared(self.browser.email):
                self.logger.info("Proactively checking for suspicious activity alerts...")
                if self._check_suspicious_activity_proactively():
                    self.logger.info("Suspicious activity detected during proactive check")
                    return self._handle_suspicious_activity()

            if self._detect_suspicious_activity():
                self.logger.info("Suspicious activity detected - handling before other actions")
                return self._handle_suspicious_activity()

            # Check if we're on login/challenge pages that require full security checks
            challenge_urls = [
                "signin/challenge/iap",
                "signin/challenge/ipp",
                "signin/v2/challenge",
                "accounts.google.com/signin"
            ]

            is_challenge_page = any(challenge_url in current_url for challenge_url in challenge_urls)

            if is_challenge_page:
                self.logger.info(f"On challenge page: {current_url} - performing full security checks")

                # Check for phone verification challenge
                if self._detect_phone_verification():
                    return self._handle_phone_verification()

                # Check for 2FA/MFA challenges
                if self._detect_two_factor_auth():
                    return self._handle_two_factor_auth()

                # Check for account recovery challenges
                if self._detect_account_recovery():
                    return self._handle_account_recovery()

                # Check for terms of service updates
                if self._detect_terms_update():
                    return self._handle_terms_update()
            else:
                # On safe pages, still check for terms updates as they can appear anywhere
                if self._detect_terms_update():
                    return self._handle_terms_update()

                self.logger.info(f"On safe Google page: {current_url} - skipping phone/2FA checks but checked suspicious activity")

            self.logger.info("No security challenges detected, proceeding with human actions")
            return False

        except Exception as e:
            self.logger.error(f"Error checking security challenges: {str(e)}")
            return False



    def _detect_phone_verification(self):
        """Detect phone number verification screen with 5sim integration support"""
        try:
            # Check URL patterns
            current_url = self.browser.this_url()
            phone_url_patterns = [
                "signin/challenge/iap",
                "signin/challenge/ipp",
                "signin/v2/challenge/selection",
                "accounts.google.com/signin/v2/challenge"
            ]

            if any(pattern in current_url for pattern in phone_url_patterns):
                self.logger.info(f"Phone verification detected via URL: {current_url}")
                return True

            # Check for French text patterns (as shown in screenshot)
            french_phone_indicators = [
                "Confirmez qu'il s'agit bien de vous",
                "Saisissez un numéro de téléphone",
                "numéro de téléphone pour recevoir un SMS",
                "code de validation",
                "Essayer une autre méthode"
            ]

            # Check for English text patterns
            english_phone_indicators = [
                "Verify it's you",
                "Enter a phone number",
                "phone number to get a text",
                "verification code",
                "Try another way",
                "2-Step Verification"
            ]

            all_indicators = french_phone_indicators + english_phone_indicators

            for indicator in all_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.info(f"Phone verification detected via text: {indicator}")
                    return True

            # Check for specific elements - prioritize phoneNumberId for 5sim integration
            phone_elements = [
                '//input[@id="phoneNumberId"]',  # Primary target for 5sim integration
                '//input[@type="tel"]',  # Phone input field
                '//input[@name="phoneNumber"]',
                '//div[contains(text(), "phone")]',
                '//button[contains(text(), "Suivant")]',  # French "Next" button
                '//button[contains(text(), "Next")]',
                '//a[contains(text(), "Try another way")]',
                '//a[contains(text(), "Essayer une autre méthode")]'  # French "Try another way"
            ]

            for xpath in phone_elements:
                # Use silent detection to avoid logging errors for expected missing elements
                element = self._enhanced_driver.find_xpath_silent(xpath)
                if element:
                    self.logger.info(f"Phone verification detected via element: {xpath}")

                    # Special handling for phoneNumberId - mark for 5sim integration
                    if xpath == '//input[@id="phoneNumberId"]':
                        self.logger.info("Detected phoneNumberId field - 5sim integration will be triggered")
                        # Store this information for the handler
                        self._phone_verification_type = "phoneNumberId"

                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting phone verification: {str(e)}")
            return False



    def _handle_phone_verification(self):
        """Handle phone verification challenge with 5sim integration"""
        self.logger.warning("### PHONE VERIFICATION REQUIRED ###")
        self.logger.warning("Google is requesting phone number verification")

        try:
            # Check if account is in cooldown due to previous failed attempts
            email = self.browser.email
            if email and self._should_skip_account_due_to_cooldown(email):
                self._close_browser_safely()
                return True  # Skip this account

            # No proxy health checks needed - using simple extension-based proxy

            # Update account status to indicate phone verification needed
            self.update_email_status(self.browser.email, "phone_verification_required")

            # Check if we have a phone number in the account data first
            phone_number = self._get_account_phone_number()

            if phone_number:
                self.logger.info(f"Found phone number for account: {phone_number}")
                return self._attempt_phone_verification(phone_number)

            # No phone number available - try 5sim integration if available
            if FIVESIM_AVAILABLE:
                self.logger.info("No phone number in account data - attempting 5sim integration")
                return self._handle_phone_verification_with_5sim()
            else:
                self.logger.warning("5sim integration not available")
                return self._handle_phone_verification_without_number()
        except Exception as e:
            self.logger.error(f"Error handling phone verification: {str(e)}")
            return True
        finally:
            # No proxy health checks needed - using simple extension-based proxy
            pass



    def _get_account_phone_number(self):
        """Get phone number from account data"""
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for account in data:
                if account.get('email') == self.browser.email:
                    phone = account.get('phone', '')
                    if phone and not '@' in phone:  # Make sure it's a phone, not email
                        return phone

            return None

        except Exception as e:
            self.logger.error(f"Error getting phone number: {str(e)}")
            return None



    def _handle_phone_verification_with_5sim(self):
        """Handle phone verification using 5sim service with enhanced retry logic"""
        try:
            # No proxy health checks needed - using simple extension-based proxy

            # Initialize 5sim manager
            fivesim_manager = FiveSimManager(self.logger)

            if not fivesim_manager.is_available():
                self.logger.warning("5sim integration not available or not configured")
                return self._handle_phone_verification_without_number()

            # Enhanced retry logic with new phone numbers
            max_phone_attempts = 2  # Try up to 2 different phone numbers

            for attempt in range(max_phone_attempts):
                self.logger.info(f"Phone verification attempt {attempt + 1}/{max_phone_attempts}")

                # Get phone number from 5sim
                self.logger.info("Requesting phone number from 5sim...")
                phone_number = fivesim_manager.get_phone_number_for_gmail()

                if not phone_number:
                    self.logger.error(f"Failed to get phone number from 5sim (attempt {attempt + 1})")
                    if attempt < max_phone_attempts - 1:
                        self.logger.info("Retrying with new phone number...")
                        sleep(uniform(5.0, 10.0))  # Wait before retry
                        continue
                    else:
                        return self._handle_phone_verification_without_number()

                # Attempt to enter the phone number
                self.logger.info(f"Entering phone number: {phone_number}")
                success = self._attempt_phone_verification(phone_number)

                if not success:
                    self.logger.error("Failed to enter phone number")
                    fivesim_manager.cancel_current_order()
                    if attempt < max_phone_attempts - 1:
                        self.logger.info("Retrying with new phone number...")
                        sleep(uniform(5.0, 10.0))
                        continue
                    else:
                        return self._handle_phone_verification_without_number()

                # Wait for SMS verification code and enter it automatically
                self.logger.info("Waiting for SMS verification code...")
                code_success = self._wait_and_enter_sms_code_with_5sim_enhanced(fivesim_manager, attempt + 1)

                if code_success:
                    # Mark order as finished
                    fivesim_manager.finish_current_order()

                    # Update SMS verification status tracking
                    self._update_sms_verification_status(
                        email=self.browser.email,
                        status=True,
                        phone_number=phone_number,
                        verification_method='5sim'
                    )

                    self.logger.info("Phone verification completed successfully with 5sim")
                    return True
                else:
                    self.logger.error(f"Failed to enter SMS verification code (attempt {attempt + 1})")
                    fivesim_manager.cancel_current_order()

                    if attempt < max_phone_attempts - 1:
                        self.logger.info("Retrying with new phone number...")
                        sleep(uniform(10.0, 15.0))  # Longer wait between phone number attempts
                        continue
                    else:
                        self.logger.error("All phone verification attempts failed")
                        return self._handle_phone_verification_without_number()

            return self._handle_phone_verification_without_number()

        except Exception as e:
            self.logger.error(f"Error in 5sim phone verification: {str(e)}")
            return self._handle_phone_verification_without_number()
        finally:
            # No proxy health checks needed - using simple extension-based proxy
            pass




    def _wait_and_enter_sms_code_with_5sim(self, fivesim_manager):
        """Wait for SMS input field and automatically retrieve and enter code from 5sim"""
        try:
            self.logger.info("Waiting for SMS code input field to appear...")

            # Wait for SMS code input field to appear (up to 60 seconds)
            max_wait_for_field = 60
            wait_time = 0
            sms_input = None

            # Updated SMS selectors to match actual HTML structure
            sms_selectors = [
                # Priority selectors based on actual HTML structure
                '//input[@id="idvAnyPhonePin"]',                           # Actual Google SMS input ID
                '//input[@name="pin"]',                                    # Actual name attribute
                '//input[@type="tel" and contains(@aria-label, "Enter code")]',  # Actual type and aria-label
                '//input[@type="tel" and @pattern="[0-9 ]*"]',            # Actual pattern attribute//input[contains(@class, "verification")]]

                # Generic tel input selectors
                '//input[@type="tel"]',                                    # Generic tel input
                '//input[@type="tel" and @maxlength="6"]',                # 6-digit tel codes
                '//input[@type="tel" and @maxlength="8"]',                # 8-digit tel codes

                # Legacy text input selectors (fallback)
                '//input[@type="text" and contains(@placeholder, "code")]',
                '//input[@name="smsUserPin"]',
                '//input[@id="smsUserPin"]',
                '//input[contains(@placeholder, "verification")]',
                '//input[contains(@placeholder, "validation")]',
                '//input[contains(@placeholder, "vérification")]',        # French
                '//input[@type="text" and @maxlength="6"]',               # 6-digit text codes
                '//input[@type="text" and @maxlength="8"]'                # 8-digit text codes
            ]

            while wait_time < max_wait_for_field and not sms_input:
                for selector in sms_selectors:
                    try:
                        sms_input = self.browser.find_xpath(selector)
                        if sms_input:
                            self.logger.info(f"Found SMS code input field: {selector}")
                            break
                    except:
                        continue

                if not sms_input:
                    sleep(2)
                    wait_time += 2

            if not sms_input:
                self.logger.error("SMS code input field not found after 60 seconds")
                fivesim_manager.cancel_current_order()
                return False

            # Now wait for SMS code from 5sim and enter it
            self.logger.info("SMS input field found, now waiting for SMS code from 5sim...")

            # Wait for SMS code with timeout
            verification_code = fivesim_manager.wait_for_verification_code(timeout=300)  # 5 minutes

            if not verification_code:
                self.logger.error("SMS verification code not received from 5sim")
                fivesim_manager.cancel_current_order()
                return False

            # Enter the verification code
            self.logger.info(f"Received SMS code: {verification_code}")
            self.logger.info("Entering verification code into input field...")

            # Clear and enter verification code
            sms_input.clear()
            sleep(uniform(0.5, 1.0))

            if hasattr(self.browser, 'human_type_text'):
                self.browser.human_type_text(sms_input, verification_code, clear_first=False)
            else:
                sms_input.send_keys(verification_code)

            sleep(uniform(1.0, 2.0))

            # Try to submit the code
            submit_success = self._submit_sms_verification_code(sms_input)

            if submit_success:
                # Mark order as finished
                fivesim_manager.finish_current_order()
                self.logger.info("SMS verification completed successfully with 5sim")
                return True
            else:
                self.logger.error("Failed to submit SMS verification code")
                fivesim_manager.cancel_current_order()
                return False

        except Exception as e:
            self.logger.error(f"Error in SMS code handling with 5sim: {str(e)}")
            fivesim_manager.cancel_current_order()
            return False
        

    def _wait_and_enter_sms_code_with_5sim_enhanced(self, fivesim_manager, attempt_number):
        """Enhanced SMS code waiting with better retry logic and error handling"""
        try:
            self.logger.info(f"Waiting for SMS code input field to appear (attempt {attempt_number})...")

            # Wait for SMS code input field to appear (up to 60 seconds)
            max_wait_for_field = 20
            wait_time = 0
            sms_input = None

            # Enhanced SMS selectors with comprehensive coverage
            sms_selectors = [
                # Priority selectors based on actual HTML structure
                # Generic tel input selectors
                '//input[@type="tel"]',                                    # Generic tel input codes

                # Additional fallback selectors
                '//input[contains(@class, "verification")]',               # Class-based selection
                '//input[contains(@class, "sms")]',                       # SMS-related classes
                '//input[contains(@name, "code")]',                       # Name containing 'code'
                '//input[contains(@id, "code")]'                          # ID containing 'code'
            ]

            while wait_time < max_wait_for_field and not sms_input:
                for selector in sms_selectors:
                    try:
                        sms_input = self.browser.find_xpath(selector)
                        if sms_input:
                            self.logger.info(f"Found SMS code input field: {selector}")
                            break
                    except:
                        continue

                if not sms_input:
                    sleep(2)
                    wait_time += 2

            if not sms_input:
                self.logger.error("SMS code input field not found after 60 seconds")
                fivesim_manager.cancel_current_order()
                return False

            # Enhanced SMS code retrieval with multiple attempts
            self.logger.info(f"SMS input field found, now waiting for SMS code from 5sim (attempt {attempt_number})...")

            # Log current order details for debugging
            if hasattr(fivesim_manager, 'current_order') and fivesim_manager.current_order:
                order = fivesim_manager.current_order
                self.logger.info(f"Current 5sim order - ID: {order.id}, Phone: {order.phone}, Status: {order.status}")

            # Try to get SMS code with shorter timeout for faster retry
            verification_code = fivesim_manager.wait_for_verification_code(timeout=180)  # 3 minutes instead of 5

            if not verification_code:
                self.logger.error(f"SMS verification code not received from 5sim (attempt {attempt_number})")
                self.logger.info(f"SMS verification failed for {self.browser.email} - will retry with new phone number")
                fivesim_manager.cancel_current_order()
                return False

            # Enter the verification code with enhanced error handling
            self.logger.info(f"Received SMS code: {verification_code}")
            self.logger.info("Entering verification code into input field...")

            # Clear and enter verification code
            sms_input.clear()
            sleep(uniform(0.5, 1.0))

            if hasattr(self.browser, 'human_type_text'):
                self.browser.human_type_text(sms_input, verification_code, clear_first=False)
            else:
                sms_input.send_keys(verification_code)

            sleep(uniform(1.0, 2.0))

            # Try to submit the code with enhanced error handling
            submit_success = self._submit_sms_verification_code_enhanced(sms_input, attempt_number)

            if submit_success:
                self.logger.info(f"SMS verification completed successfully with 5sim (attempt {attempt_number})")
                return True
            else:
                self.logger.error(f"Failed to submit SMS verification code (attempt {attempt_number})")
                return False

        except Exception as e:
            self.logger.error(f"Error in enhanced SMS code handling with 5sim: {str(e)}")
            fivesim_manager.cancel_current_order()
            return False

    def _submit_sms_verification_code(self, sms_input):
        """Submit the SMS verification code"""
        try:
            # Try to click Next/Continue/Verify button
            submit_buttons = [
                '//button[contains(text(), "Next")]',
                '//button[contains(text(), "Continue")]',
                '//button[contains(text(), "Suivant")]',  # French
                '//button[contains(text(), "Continuer")]',  # French
                '//button[contains(text(), "Vérifier")]',  # French Verify
                '//*[@id="next"]',
                '//div[@role="button" and contains(text(), "Next")]',
                '//div[@role="button" and contains(text(), "Suivant")]'
            ]

            for button_xpath in submit_buttons:
                try:
                    button = self.browser.find_xpath(button_xpath)
                    if button:
                        self.logger.info(f"Found submit button: {button_xpath}")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        self.logger.info("SMS verification code submitted successfully")
                        sleep(uniform(2.0, 4.0))
                        return True
                except Exception as e:
                    self.logger.debug(f"Submit button failed: {button_xpath} - {str(e)}")
                    continue

            # If no button found, try pressing Enter
            try:
                self.logger.info("No submit button found, trying Enter key...")
                sms_input.send_keys(Keys.RETURN)
                self.logger.info("SMS verification code submitted via Enter key")
                sleep(uniform(2.0, 4.0))
                return True
            except Exception as e:
                self.logger.warning(f"Enter key submission failed: {str(e)}")

            self.logger.warning("Could not find submit button or use Enter key for SMS verification")
            return False

        except Exception as e:
            self.logger.error(f"Error submitting SMS verification code: {str(e)}")
            return False

    def _submit_sms_verification_code_enhanced(self, sms_input, attempt_number):
        """Enhanced SMS verification code submission with comprehensive button detection and staleness handling"""
        try:
            self.logger.info(f"Attempting to submit SMS verification code (attempt {attempt_number})")

            # Wait a moment for the page to stabilize after code entry
            sleep(uniform(1.0, 2.0))

            # Comprehensive submit button selectors for modern Google verification pages
            submit_buttons = [
                # Modern Material Design 3 selectors (highest priority)
                '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@class, "VfPpkd-kJFWHd")]//span[contains(text(), "Next")]',
                '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@class, "VfPpkd-kJFWHd")]//span[contains(text(), "Continue")]',
                '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@class, "VfPpkd-kJFWHd")]//span[contains(text(), "Verify")]',
                '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@class, "VfPpkd-kJFWHd")]//span[contains(text(), "Suivant")]',

                # Material Design button with data attributes
                '//button[@data-mdc-dialog-action="ok"]',
                '//button[@data-mdc-dialog-action="next"]',
                '//button[@data-mdc-dialog-action="continue"]',

                # Google-specific button patterns
                '//button[contains(@class, "VfPpkd-LgbsSe") and @jsname]',
                '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@aria-label, "Next")]',
                '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@aria-label, "Continue")]',

                # Modern div-based buttons with role
                '//div[@role="button" and contains(@class, "VfPpkd-LgbsSe")]//span[contains(text(), "Next")]',
                '//div[@role="button" and contains(@class, "VfPpkd-LgbsSe")]//span[contains(text(), "Continue")]',
                '//div[@role="button" and contains(@class, "VfPpkd-Bz112c")]',

                # Generic Material Design patterns
                '//button[contains(@class, "mdc-button") and contains(@class, "mdc-button--raised")]',
                '//button[contains(@class, "mdc-button") and contains(@class, "mdc-button--unelevated")]',

                # Text-based selectors (medium priority)
                '//button[contains(text(), "Next")]',
                '//button[contains(text(), "Continue")]',
                '//button[contains(text(), "Verify")]',
                '//button[contains(text(), "Submit")]',
                '//button[contains(text(), "Suivant")]',  # French
                '//button[contains(text(), "Continuer")]',  # French
                '//button[contains(text(), "Vérifier")]',  # French Verify

                # Span-based text with parent button
                '//span[contains(text(), "Next")]/parent::button',
                '//span[contains(text(), "Continue")]/parent::button',
                '//span[contains(text(), "Verify")]/parent::button',
                '//span[contains(text(), "Suivant")]/parent::button',

                # Legacy selectors (lower priority)
                '//input[@type="submit"]',
                '//button[@type="submit"]',
                '//*[@id="next"]',
                '//*[@id="continue"]',
                '//*[@id="submit"]',

                # Role-based selectors
                '//div[@role="button" and contains(text(), "Next")]',
                '//div[@role="button" and contains(text(), "Continue")]',
                '//div[@role="button" and contains(text(), "Suivant")]',

                # Fallback class-based selectors
                '//button[contains(@class, "VfPpkd-LgbsSe")]',
                '//div[contains(@class, "VfPpkd-LgbsSe") and @role="button"]',
                '//button[contains(@class, "primary")]',
                '//button[contains(@class, "submit")]'
            ]

            button_found = False
            successful_selector = None

            # Try each selector with enhanced error handling
            for i, button_xpath in enumerate(submit_buttons):
                try:
                    self.logger.debug(f"Trying selector {i+1}/{len(submit_buttons)}: {button_xpath}")

                    # Use WebDriverWait for better element detection
                    try:
                        from selenium.webdriver.support.ui import WebDriverWait
                        from selenium.webdriver.support import expected_conditions as EC
                        from selenium.webdriver.common.by import By

                        # Convert XPath to By locator
                        wait = WebDriverWait(self.browser.driver, 2)
                        button = wait.until(EC.element_to_be_clickable((By.XPATH, button_xpath)))
                    except:
                        # Fallback to direct find
                        button = self.browser.find_xpath(button_xpath)

                    if button and button.is_enabled() and button.is_displayed():
                        self.logger.info(f"Found enabled and visible submit button: {button_xpath}")
                        successful_selector = button_xpath

                        # Scroll button into view if needed
                        try:
                            self.browser.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                            sleep(0.5)
                        except:
                            pass

                        # Click the button
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        self.logger.info(f"SMS verification code submitted successfully (attempt {attempt_number}) using selector: {successful_selector}")

                        # Wait for page transition or verification completion
                        self._wait_for_verification_completion_or_next_step()

                        button_found = True
                        break

                except Exception as e:
                    self.logger.debug(f"Submit button selector failed: {button_xpath} - {str(e)}")
                    continue

            # If no button found, try Enter key with element staleness handling
            if not button_found:
                self.logger.info("No submit button found, trying Enter key with staleness handling...")

                try:
                    # Re-find the SMS input element to handle staleness
                    fresh_sms_input = self._refind_sms_input_element()

                    if fresh_sms_input:
                        self.logger.info("Re-found SMS input element, attempting Enter key submission...")
                        fresh_sms_input.send_keys(Keys.RETURN)
                        self.logger.info(f"SMS verification code submitted via Enter key (attempt {attempt_number})")

                        # Wait for page transition or verification completion
                        self._wait_for_verification_completion_or_next_step()

                        return True
                    else:
                        self.logger.warning("Could not re-find SMS input element for Enter key submission")

                except Exception as e:
                    self.logger.warning(f"Enter key submission failed: {str(e)}")

            # If still no success, capture page HTML for debugging
            if not button_found:
                self._capture_verification_page_debug_info(attempt_number)
                self.logger.warning(f"Could not find submit button or use Enter key for SMS verification (attempt {attempt_number})")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error submitting SMS verification code (attempt {attempt_number}): {str(e)}")
            return False

    def _refind_sms_input_element(self):
        """Re-find SMS input element to handle staleness issues"""
        try:
            # Use the same comprehensive selectors as in the main SMS detection
            sms_selectors = [
                '//input[@id="idvAnyPhonePin"]',
                '//input[@name="pin"]',
                '//input[@type="tel" and contains(@aria-label, "Enter code")]',
                '//input[@type="tel" and @pattern="[0-9 ]*"]',
                '//input[contains(@aria-label, "verification code")]',
                '//input[contains(@placeholder, "Enter code")]',
                '//input[@type="tel"]',
                '//input[@type="text" and contains(@placeholder, "code")]'
            ]

            for selector in sms_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element and element.is_displayed():
                        self.logger.debug(f"Re-found SMS input using selector: {selector}")
                        return element
                except:
                    continue

            self.logger.warning("Could not re-find SMS input element")
            return None

        except Exception as e:
            self.logger.error(f"Error re-finding SMS input element: {str(e)}")
            return None

    def _capture_verification_page_debug_info(self, attempt_number):
        """Capture page HTML and structure for debugging submit button issues"""
        try:
            self.logger.info(f"Capturing verification page debug info (attempt {attempt_number})...")

            # Get current page URL
            current_url = self.browser.driver.current_url
            self.logger.info(f"Current page URL: {current_url}")

            # Get page title
            page_title = self.browser.driver.title
            self.logger.info(f"Page title: {page_title}")

            # Find all buttons on the page
            try:
                all_buttons = self.browser.driver.find_elements(By.TAG_NAME, "button")
                self.logger.info(f"Found {len(all_buttons)} button elements on page")

                for i, button in enumerate(all_buttons[:10]):  # Limit to first 10 buttons
                    try:
                        button_text = button.text.strip()
                        button_class = button.get_attribute("class")
                        button_id = button.get_attribute("id")
                        button_type = button.get_attribute("type")
                        is_enabled = button.is_enabled()
                        is_displayed = button.is_displayed()

                        self.logger.info(f"Button {i+1}: text='{button_text}', class='{button_class}', id='{button_id}', type='{button_type}', enabled={is_enabled}, displayed={is_displayed}")
                    except Exception as btn_e:
                        self.logger.debug(f"Error analyzing button {i+1}: {str(btn_e)}")

            except Exception as e:
                self.logger.warning(f"Could not analyze buttons: {str(e)}")

            # Find all div elements with role="button"
            try:
                div_buttons = self.browser.driver.find_elements(By.XPATH, '//div[@role="button"]')
                self.logger.info(f"Found {len(div_buttons)} div elements with role='button'")

                for i, div_btn in enumerate(div_buttons[:5]):  # Limit to first 5
                    try:
                        div_text = div_btn.text.strip()
                        div_class = div_btn.get_attribute("class")
                        div_id = div_btn.get_attribute("id")
                        is_displayed = div_btn.is_displayed()

                        self.logger.info(f"Div button {i+1}: text='{div_text}', class='{div_class}', id='{div_id}', displayed={is_displayed}")
                    except Exception as div_e:
                        self.logger.debug(f"Error analyzing div button {i+1}: {str(div_e)}")

            except Exception as e:
                self.logger.warning(f"Could not analyze div buttons: {str(e)}")

            # Save page source for detailed analysis
            try:
                page_source = self.browser.driver.page_source

                # Look for specific patterns in the HTML
                if 'VfPpkd-LgbsSe' in page_source:
                    self.logger.info("Found Material Design button classes (VfPpkd-LgbsSe) in page source")
                if 'mdc-button' in page_source:
                    self.logger.info("Found MDC button classes in page source")
                if 'Next' in page_source:
                    self.logger.info("Found 'Next' text in page source")
                if 'Continue' in page_source:
                    self.logger.info("Found 'Continue' text in page source")

                # Save to debug file
                debug_file = f"grps/PyFiles/debug_verification_page_{attempt_number}.html"
                with open(debug_file, 'w', encoding='utf-8') as f:
                    f.write(page_source)
                self.logger.info(f"Full page source saved to: {debug_file}")

            except Exception as e:
                self.logger.warning(f"Could not capture page source: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error capturing debug info: {str(e)}")

    def _wait_for_verification_completion_or_next_step(self):
        """Wait for verification completion or next step after SMS code submission"""
        try:
            self.logger.info("Waiting for verification completion or next step...")

            # Wait for page transition indicators
            max_wait = 10  # seconds
            wait_time = 0

            while wait_time < max_wait:
                try:
                    # Check if we're still on the verification page
                    current_url = self.browser.driver.current_url

                    # Look for signs that verification completed
                    completion_indicators = [
                        # URL changes indicating success
                        'myaccount.google.com',
                        'accounts.google.com/signin/v2/challenge/selection',
                        'mail.google.com',
                        'gmail.com',

                        # Page elements indicating success or next step
                        '//div[contains(text(), "verified")]',
                        '//div[contains(text(), "success")]',
                        '//h1[contains(text(), "Welcome")]',
                        '//div[contains(@class, "success")]'
                    ]

                    # Check URL changes
                    for indicator in completion_indicators[:4]:  # URL indicators
                        if indicator in current_url:
                            self.logger.info(f"Verification appears successful - URL contains: {indicator}")
                            sleep(uniform(2.0, 3.0))  # Additional wait for page stability
                            return True

                    # Check page elements
                    for indicator in completion_indicators[4:]:  # Element indicators
                        try:
                            if self.browser.find_xpath(indicator):
                                self.logger.info(f"Verification appears successful - found element: {indicator}")
                                sleep(uniform(2.0, 3.0))
                                return True
                        except:
                            continue

                    # Check if SMS input field is gone (indicating progression)
                    try:
                        sms_input = self.browser.find_xpath('//input[@type="tel"]')
                        if not sms_input:
                            self.logger.info("SMS input field no longer present - verification may have progressed")
                            sleep(uniform(2.0, 3.0))
                            return True
                    except:
                        # If we can't find SMS input, that might be good
                        self.logger.info("Cannot find SMS input field - verification likely progressed")
                        sleep(uniform(2.0, 3.0))
                        return True

                    sleep(1)
                    wait_time += 1

                except Exception as e:
                    self.logger.debug(f"Error checking verification completion: {str(e)}")
                    sleep(1)
                    wait_time += 1

            # Default wait time even if we can't detect completion
            self.logger.info("Verification completion detection timeout - using default wait")
            sleep(uniform(3.0, 5.0))
            return True

        except Exception as e:
            self.logger.warning(f"Error waiting for verification completion: {str(e)}")
            sleep(uniform(3.0, 5.0))  # Default wait
            return True

    def _enter_sms_verification_code(self, verification_code):
        """Enter SMS verification code into the input field"""
        try:
            self.logger.info(f"Entering SMS verification code: {verification_code}")

            # Wait for SMS code input field to appear
            max_wait = 30
            wait_time = 0
            sms_input = None

            # Updated SMS selectors to match actual HTML structure
            sms_selectors = [
                # Priority selectors based on actual HTML structure
                '//input[@id="idvAnyPhonePin"]',                           # Actual Google SMS input ID
                '//input[@name="pin"]',                                    # Actual name attribute
                '//input[@type="tel" and contains(@aria-label, "Enter code")]',  # Actual type and aria-label
                '//input[@type="tel" and @pattern="[0-9 ]*"]',            # Actual pattern attribute

                # Generic tel input selectors
                '//input[@type="tel"]',                                    # Generic tel input
                '//input[@type="tel" and @maxlength="6"]',                # 6-digit tel codes
                '//input[@type="tel" and @maxlength="8"]',                # 8-digit tel codes

                # Legacy text input selectors (fallback)
                '//input[@type="text" and contains(@placeholder, "code")]',
                '//input[@name="smsUserPin"]',
                '//input[@id="smsUserPin"]',
                '//input[contains(@placeholder, "verification")]',
                '//input[contains(@placeholder, "validation")]',
                '//input[@type="text" and @maxlength="6"]',               # 6-digit text codes
                '//input[@type="text" and @maxlength="8"]'                # 8-digit text codes
            ]

            while wait_time < max_wait and not sms_input:
                for selector in sms_selectors:
                    try:
                        sms_input = self.browser.find_xpath(selector)
                        if sms_input:
                            self.logger.info(f"Found SMS code input field: {selector}")
                            break
                    except:
                        continue

                if not sms_input:
                    sleep(1)
                    wait_time += 1

            if sms_input:
                # Clear and enter verification code
                sms_input.clear()
                sleep(uniform(0.5, 1.0))

                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(sms_input, verification_code, clear_first=False)
                else:
                    sms_input.send_keys(verification_code)

                sleep(uniform(1.0, 2.0))

                # Try to click Next/Continue/Verify button
                submit_buttons = [
                    '//button[contains(text(), "Next")]',
                    '//button[contains(text(), "Continue")]',
                    '//button[contains(text(), "Verify")]',
                    '//button[contains(text(), "Submit")]',
                    '//button[contains(text(), "Suivant")]',  # French
                    '//button[contains(text(), "Continuer")]',  # French
                    '//input[@type="submit"]',
                    '//button[@type="submit"]'
                ]

                for button_xpath in submit_buttons:
                    try:
                        button = self.browser.find_xpath(button_xpath)
                        if button:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(button)
                            else:
                                button.click()

                            self.logger.info("SMS verification code submitted successfully")
                            sleep(uniform(2.0, 4.0))
                            return True
                    except:
                        continue

                # If no button found, try pressing Enter
                try:
                    sms_input.send_keys(Keys.RETURN)
                    self.logger.info("SMS verification code submitted via Enter key")
                    sleep(uniform(2.0, 4.0))
                    return True
                except:
                    pass

                self.logger.warning("Could not find submit button for SMS verification code")
                return False
            else:
                self.logger.error("SMS code input field not found")
                return False

        except Exception as e:
            self.logger.error(f"Error entering SMS verification code: {str(e)}")
            return False

    def _attempt_phone_verification(self, phone_number):
        """Attempt to enter phone number for verification"""
        try:
            self.logger.info(f"Attempting phone verification with: {phone_number}")

            # Try to find phone input field
            phone_input = None
            phone_selectors = [
                '//input[@type="tel"]',
                '//input[@name="phoneNumber"]',
                '//input[@id="phoneNumberId"]',
                '//input[contains(@placeholder, "phone")]',
                '//input[contains(@placeholder, "téléphone")]'
            ]

            for selector in phone_selectors:
                # Use silent detection to avoid logging errors for expected missing elements
                phone_input = self._enhanced_driver.find_xpath_silent(selector)
                if phone_input:
                    break

            if phone_input:
                # Clear and enter phone number with optimized timing
                phone_input.clear()
                sleep(uniform(0.5, 1.0))  # Reduced initial delay

                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(phone_input, phone_number, clear_first=False)
                else:
                    phone_input.send_keys(phone_number)

                self.logger.info(f"Phone number entered: {phone_number}")
                sleep(uniform(0.3, 0.7))  # Further reduced delay for faster processing

                # Try to click Next/Continue button with improved reliability and logging
                button_clicked = False
                buttons_found = 0
                buttons_attempted = 0

                next_buttons = [
                    '//button[contains(text(), "Suivant")]',  # French
                    '//button[contains(text(), "Next")]',     # English
                    '//button[contains(text(), "Continuer")]',  # French Continue
                    '//*[@id="identifierNext"]',  # Google's common Next button ID
                    '//div[@role="button" and contains(text(), "Suivant")]',  # Div buttons
                    '//span[contains(text(), "Suivant")]/parent::button',  # Button with span
                    '//span[contains(text(), "Next")]/parent::button',  # Button with span - Fixed
                    '//button[@type="submit"]',  # Generic submit button
                ]

                self.logger.info(f"Searching for Next button using {len(next_buttons)} selectors...")

                for button_xpath in next_buttons:
                    try:
                        # Use silent detection to avoid logging errors for expected missing elements
                        button = self._enhanced_driver.find_xpath_silent(button_xpath)
                        if button:
                            buttons_found += 1
                            buttons_attempted += 1
                            self.logger.info(f"Found Next button with selector: {button_xpath} (Button {buttons_found}/{len(next_buttons)})")

                            # Try multiple click strategies with reduced timeouts for faster processing
                            click_success = False

                            # Strategy 1: Quick clickability check with reduced timeout
                            try:
                                from selenium.webdriver.support.ui import WebDriverWait
                                from selenium.webdriver.support import expected_conditions as EC
                                from selenium.webdriver.common.by import By

                                # Reduced timeout from 5 to 2 seconds for faster processing
                                wait = WebDriverWait(self.browser, 2)
                                clickable_button = wait.until(EC.element_to_be_clickable((By.XPATH, button_xpath)))

                                # Try human click first
                                if hasattr(self.browser, 'human_click_element'):
                                    self.browser.human_click_element(clickable_button)
                                else:
                                    clickable_button.click()

                                self.logger.info("Phone number submitted via button click (Strategy 1)")
                                click_success = True

                            except Exception as wait_error:
                                self.logger.info(f"Strategy 1 failed for {button_xpath}: {str(wait_error)}")

                                # Strategy 2: Direct JavaScript click without wait
                                try:
                                    self.browser.execute_script("arguments[0].click();", button)
                                    self.logger.info("Phone number submitted via JavaScript click (Strategy 2)")
                                    click_success = True

                                except Exception as js_error:
                                    self.logger.info(f"Strategy 2 failed for {button_xpath}: {str(js_error)}")

                                    # Strategy 3: Force click with scroll into view
                                    try:
                                        self.browser.execute_script("arguments[0].scrollIntoView(true);", button)
                                        sleep(0.2)  # Brief pause after scroll
                                        self.browser.execute_script("arguments[0].click();", button)
                                        self.logger.info("Phone number submitted via force click (Strategy 3)")
                                        click_success = True

                                    except Exception as force_error:
                                        self.logger.info(f"Strategy 3 failed for {button_xpath}: {str(force_error)}")

                            # If any strategy succeeded, break out of the loop
                            if click_success:
                                sleep(uniform(0.5, 1.0))  # Reduced delay for faster processing
                                button_clicked = True
                                break
                            else:
                                self.logger.warning(f"All click strategies failed for button: {button_xpath}")
                                # Continue to next button selector instead of giving up

                    except Exception as e:
                        self.logger.info(f"Button detection failed: {button_xpath} - {str(e)}")
                        continue

                # Log button detection summary
                self.logger.info(f"Button detection summary: {buttons_found} buttons found, {buttons_attempted} attempted")

                # If no button was clicked, try pressing Enter as fallback
                if not button_clicked:
                    if buttons_found > 0:
                        self.logger.warning(f"Found {buttons_found} Next button(s) but all click attempts failed, trying Enter key as fallback...")
                    else:
                        self.logger.warning("No Next buttons found on page, trying Enter key as fallback...")
                    try:
                        # Ensure phone input is still focused before sending Enter
                        phone_input.click()  # Refocus the input
                        sleep(0.2)  # Brief pause to ensure focus
                        phone_input.send_keys(Keys.RETURN)
                        self.logger.info("Phone number submitted via Enter key fallback")
                        sleep(uniform(0.5, 1.0))  # Reduced delay for faster processing
                        button_clicked = True  # Mark as successful to prevent further attempts
                    except Exception as e:
                        self.logger.error(f"Enter key fallback also failed: {str(e)}")
                        self.logger.warning("All submission methods failed, but continuing anyway...")
                        # Continue anyway, maybe the form was submitted by some other means

                # Wait for SMS code input or next step with timeout
                try:
                    self._wait_for_sms_code_input()
                    return True
                except Exception as wait_error:
                    self.logger.warning(f"SMS code input wait failed: {str(wait_error)}")
                    # Continue anyway as the form might have been submitted successfully
                    return True

            else:
                self.logger.error("Could not find phone input field")
                return True

        except Exception as e:
            self.logger.error(f"Error attempting phone verification: {str(e)}")
            return True


    def _wait_for_sms_code_input(self):
        """Wait for SMS code input field to appear"""
        try:
            self.logger.info("Waiting for SMS code input field...")

            # Wait up to 30 seconds for SMS code field
            max_wait = 30
            wait_time = 0

            while wait_time < max_wait:
                # Check for SMS code input field - Updated to match actual HTML structure
                sms_selectors = [
                    # Priority selectors based on actual HTML structure
                    '//input[@name="pin"]',                                    # Actual name attribute
                    '//input[@type="tel" and contains(@aria-label, "Enter code")]',  # Actual type and aria-label
                    '//input[@type="tel" and @pattern="[0-9 ]*"]',            # Actual pattern attribute

                    # Generic tel input selectors
                    '//input[@type="tel"]',                                    # Generic tel input
                    '//input[@type="tel" and @maxlength="6"]',                # 6-digit tel codes
                    '//input[@type="tel" and @maxlength="8"]',                # 8-digit tel codes

                    # Legacy text input selectors (fallback)
                    '//input[@type="text" and contains(@placeholder, "code")]',
                    '//input[@name="smsUserPin"]',
                    '//input[@id="smsUserPin"]',
                    '//input[contains(@placeholder, "verification")]',
                    '//input[contains(@placeholder, "validation")]',
                    '//input[@type="text" and @maxlength="6"]',               # 6-digit text codes
                    '//input[@type="text" and @maxlength="8"]'                # 8-digit text codes
                ]

                for selector in sms_selectors:
                    try:
                        if self.browser.find_xpath(selector):
                            self.logger.info("SMS code input field detected")
                            self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
                            self.logger.warning("Please enter the SMS verification code manually")
                            self.logger.warning("The script will wait for manual completion...")

                            # Wait for user to complete verification
                            self._wait_for_verification_completion()
                            return
                    except:
                        continue

                sleep(1)
                wait_time += 1

            self.logger.warning("SMS code input field not found within timeout")

        except Exception as e:
            self.logger.error(f"Error waiting for SMS code: {str(e)}")


    def _wait_for_verification_completion(self):
        """Wait for user to complete phone verification manually"""
        try:
            self.logger.info("Waiting for manual phone verification completion...")

            max_wait = 300  # 5 minutes
            wait_time = 0

            while wait_time < max_wait:
                current_url = self.browser.this_url()

                # Check if we're past the verification screen
                if not any(pattern in current_url for pattern in [
                    "signin/challenge/iap",
                    "signin/challenge/ipp",
                    "signin/v2/challenge"
                ]):
                    self.logger.info("Phone verification appears to be completed")
                    return

                # Check for success indicators
                success_indicators = [
                    "myaccount.google.com",
                    "accounts.google.com/ManageAccount",
                    "mail.google.com"
                ]

                if any(indicator in current_url for indicator in success_indicators):
                    self.logger.info("Successfully completed phone verification")
                    return

                sleep(2)
                wait_time += 2

            self.logger.warning("Phone verification timeout reached")

        except Exception as e:
            self.logger.error(f"Error waiting for verification completion: {str(e)}")


    def _handle_phone_verification_without_number(self):
        """Handle phone verification when no phone number is available"""
        try:
            self.logger.warning("No phone number available - looking for alternative methods")

            # PRIORITY 1: Try email confirmation first if available
            if self._detect_email_confirmation_option():
                self.logger.info("Email confirmation option detected - trying as alternative to phone verification")
                if self._handle_email_confirmation():
                    self.logger.info("Email confirmation completed successfully as phone verification alternative")
                    return True
                else:
                    self.logger.warning("Email confirmation failed, trying other alternatives")

            # PRIORITY 2: Try to click "Try another way" / "Essayer une autre méthode"
            alternative_buttons = [
                '//a[contains(text(), "Try another way")]',
                '//a[contains(text(), "Essayer une autre méthode")]',
                '//button[contains(text(), "Try another way")]',
                '//button[contains(text(), "Essayer une autre méthode")]',
                '//*[contains(text(), "Skip")]',
                '//*[contains(text(), "Ignorer")]'
            ]

            for button_xpath in alternative_buttons:
                try:
                    button = self.browser.find_xpath(button_xpath)
                    if button:
                        self.logger.info(f"Clicking alternative method: {button_xpath}")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(2.0, 4.0))
                        return self._handle_alternative_verification_methods()
                except:
                    continue

            # PRIORITY 3: If no alternative found, wait for manual intervention
            self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
            self.logger.warning("Phone verification required but no phone number available")
            self.logger.warning("Please complete verification manually or provide phone number")

            # Wait for manual completion
            self._wait_for_verification_completion()
            return True

        except Exception as e:
            self.logger.error(f"Error handling phone verification without number: {str(e)}")
            return True


    def _handle_alternative_verification_methods(self):
        """Handle alternative verification methods"""
        try:
            self.logger.info("Checking for alternative verification methods...")

            # PRIORITY 1: Check for email confirmation option first
            if self._detect_email_confirmation_option():
                self.logger.info("Email confirmation option detected in alternative methods")
                if self._handle_email_confirmation():
                    self.logger.info("Email confirmation completed successfully")
                    return True
                else:
                    self.logger.warning("Email confirmation failed, trying other alternatives")

            # PRIORITY 2: Look for recovery email option (legacy method)
            recovery_email_indicators = [
                "recovery email",
                "backup email",
                "email de récupération",
                "adresse e-mail de récupération"
            ]

            for indicator in recovery_email_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.info("Recovery email option detected")
                    return self._handle_recovery_email_verification()

            # PRIORITY 3: Look for security questions
            security_question_indicators = [
                "security question",
                "question de sécurité",
                "when did you create",
                "quand avez-vous créé"
            ]

            for indicator in security_question_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.info("Security question detected")
                    return self._handle_security_question()

            self.logger.warning("No alternative verification methods found")
            return True

        except Exception as e:
            self.logger.error(f"Error handling alternative verification: {str(e)}")
            return True



    def _handle_recovery_email_verification(self):
        """Handle recovery email verification"""
        try:
            self.logger.info("Attempting recovery email verification...")

            # Get recovery email from account data
            recovery_email = self._get_account_recovery_email()

            if recovery_email:
                self.logger.info(f"Using recovery email: {recovery_email}")

                # Try to find and click recovery email option
                recovery_buttons = [
                    f'//div[contains(text(), "{recovery_email}")]',
                    '//div[contains(@class, "recovery")]//button',
                    '//*[contains(text(), "Send")]',
                    '//*[contains(text(), "Envoyer")]'
                ]

                for button_xpath in recovery_buttons:
                    try:
                        button = self.browser.find_xpath(button_xpath)
                        if button:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(button)
                            else:
                                button.click()

                            self.logger.info("Recovery email verification initiated")
                            sleep(uniform(3.0, 5.0))

                            self.logger.warning("### CHECK RECOVERY EMAIL ###")
                            self.logger.warning(f"Please check {recovery_email} for verification code")

                            # Wait for manual completion
                            self._wait_for_verification_completion()
                            return True
                    except:
                        continue

            self.logger.warning("Could not initiate recovery email verification")
            return True

        except Exception as e:
            self.logger.error(f"Error handling recovery email verification: {str(e)}")
            return True



    def _get_account_recovery_email(self):
        """Get recovery email from account data"""
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for account in data:
                if account.get('email') == self.browser.email:
                    recovery = account.get('email_conf', '')
                    if recovery and '@' in recovery:  # Make sure it's an email
                        return recovery

            return None

        except Exception as e:
            self.logger.error(f"Error getting recovery email: {str(e)}")
            return None



    def _detect_email_confirmation_option(self):
        """Detect if email confirmation option is available on the verification page"""
        try:
            self.logger.info("Checking for email confirmation option...")

            # Check current URL for verification page patterns
            current_url = self.browser.this_url()
            verification_url_patterns = [
                "signin/challenge",
                "signin/v2/challenge",
                "accounts.google.com/signin/v2/challenge"
            ]

            if not any(pattern in current_url for pattern in verification_url_patterns):
                self.logger.debug("Not on a verification page")
                return False

            # XPath selectors for email confirmation options based on Google's verification page structure
            email_confirmation_selectors = [
                # Direct email confirmation button/link patterns
                "//div[contains(@data-value, 'EMAIL')]",  # Google's data-value for email option
                "//div[contains(@data-action, 'email')]",  # Alternative data-action pattern
                "//*[contains(text(), 'Get a verification code at')]",  # English: "Get a verification <NAME_EMAIL>"
                "//*[contains(text(), 'Obtenir un code de validation')]",  # French equivalent
                "//*[contains(text(), 'Confirm your recovery email')]",  # English recovery email
                "//*[contains(text(), 'Confirmez votre adresse e-mail de récupération')]",  # French recovery email
                "//*[contains(text(), 'recovery email')]",  # General recovery email text
                "//*[contains(text(), 'email de récupération')]",  # French recovery email text

                # Button/clickable elements with email-related text
                "//button[contains(text(), 'Email')]",
                "//a[contains(text(), 'Email')]",
                "//div[@role='button' and contains(text(), 'Email')]",
                "//div[@role='button' and contains(., '@')]",  # Buttons containing @ symbol (email addresses)

                # More specific patterns for Google's verification page
                "//div[contains(@class, 'VfPpkd-RLmnJb') and contains(., '@')]",  # Google Material Design button with email
                "//div[contains(@class, 'challenge-option') and contains(., '@')]",  # Challenge option with email
                "//li[contains(@class, 'challenge-option') and contains(., '@')]",  # List item challenge option

                # Pattern for masked email addresses (e.g., "6••••••••@ou••••.com")
                "//*[contains(text(), '•') and contains(text(), '@')]",
                "//*[contains(text(), '*') and contains(text(), '@')]",
            ]

            # Check each selector
            for selector in email_confirmation_selectors:
                try:
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        try:
                            element = self.browser.find_xpath(selector)
                        except:
                            element = None

                    if element and element.is_displayed():
                        self.logger.info(f"✅ Email confirmation option found using selector: {selector}")
                        return True

                except Exception as e:
                    self.logger.debug(f"Error checking email selector {selector}: {str(e)}")
                    continue

            # Check page content for email confirmation indicators
            email_indicators = [
                "verification code at",
                "code de validation à",
                "recovery email",
                "email de récupération",
                "backup email",
                "adresse e-mail de sauvegarde"
            ]

            for indicator in email_indicators:
                try:
                    if self.check_js(f'"{indicator}"'):
                        self.logger.info(f"✅ Email confirmation detected via page content: {indicator}")
                        return True
                except:
                    continue

            self.logger.debug("No email confirmation option detected")
            return False

        except Exception as e:
            self.logger.error(f"Error detecting email confirmation option: {str(e)}")
            return False
        
        

    def _handle_email_confirmation(self):
        """Handle email confirmation verification as an alternative to phone verification"""
        try:
            self.logger.info("🔄 Starting email confirmation process...")

            # Get the confirmation email from JSON file
            confirmation_email = self._get_confirmation_email_from_json()

            if not confirmation_email:
                self.logger.error("No confirmation email found in account data")
                return False

            self.logger.info(f"📧 Using confirmation email: {confirmation_email}")

            # Try to find and click the email confirmation option
            if self._click_email_confirmation_option(confirmation_email):
                self.logger.info("✅ Email confirmation option clicked successfully")

                # Handle any email input if required
                if self._enter_confirmation_email_if_needed(confirmation_email):
                    self.logger.info("✅ Email confirmation process initiated")

                    # Wait for email verification completion
                    self._wait_for_email_verification_completion(confirmation_email)
                    return True
                else:
                    self.logger.error("Failed to enter confirmation email")
                    return False
            else:
                self.logger.error("Failed to click email confirmation option")
                return False

        except Exception as e:
            self.logger.error(f"Error handling email confirmation: {str(e)}")
            return False



    def _get_confirmation_email_from_json(self):
        """Get the email_conf value from GmailAccountsMap.json for the current account"""
        try:
            gmail_map_file = os.path.join(os.path.dirname(__file__), 'json', 'GmailAccountsMap.json')

            if not os.path.exists(gmail_map_file):
                self.logger.error(f"Gmail accounts map file not found: {gmail_map_file}")
                return None

            with open(gmail_map_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            current_email = self.browser.email
            self.logger.debug(f"Looking for confirmation email for account: {current_email}")

            for account in data:
                if account.get('email') == current_email:
                    email_conf = account.get('email_conf', '').strip()

                    if email_conf and '@' in email_conf:
                        self.logger.info(f"Found confirmation email: {email_conf}")
                        return email_conf
                    else:
                        self.logger.warning(f"email_conf property is empty or invalid for {current_email}")
                        return None

            self.logger.warning(f"Account {current_email} not found in Gmail accounts map")
            return None

        except Exception as e:
            self.logger.error(f"Error reading confirmation email from JSON: {str(e)}")
            return None



    def _click_email_confirmation_option(self, confirmation_email):
        """Find and click the email confirmation option on the verification page"""
        try:
            self.logger.info("🔍 Looking for email confirmation option to click...")

            # Comprehensive list of selectors for email confirmation options
            email_option_selectors = [
                # Generic email confirmation selectors
                "//*[contains(text(), 'Confirm your recovery email')]",
                "//*[contains(text(), 'Confirmez votre adresse e-mail de récupération')]",
            ]

            # Try each selector
            for i, selector in enumerate(email_option_selectors, 1):
                try:
                    self.logger.debug(f"Trying email option selector {i}: {selector}")

                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        try:
                            element = self.browser.find_xpath(selector)
                        except:
                            element = None

                    if element and element.is_displayed():
                        self.logger.info(f"✅ Found email confirmation option using selector {i}: {selector}")

                        # Click the element
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(element)
                        else:
                            element.click()

                        sleep(uniform(2.0, 4.0))
                        self.logger.info("✅ Email confirmation option clicked successfully")
                        return True

                except Exception as e:
                    self.logger.debug(f"Error with email selector {i}: {str(e)}")
                    continue

            self.logger.warning("❌ Could not find clickable email confirmation option")
            return False

        except Exception as e:
            self.logger.error(f"Error clicking email confirmation option: {str(e)}")
            return False



    def _enter_confirmation_email_if_needed(self, confirmation_email):
        """Enter the confirmation email address if there's an input field for it"""
        try:
            self.logger.info("🔍 Checking if email input is required...")

            # Look for email input fields
            email_input_selectors = [
                "//input[@type='email']",
                "//input[@name='email']",
                "//input[@id='email']",
                "//input[contains(@placeholder, 'email')]",
                "//input[contains(@placeholder, 'Email')]",
                "//input[contains(@placeholder, '@')]",
                "//input[contains(@aria-label, 'email')]",
                "//input[contains(@aria-label, 'Email')]",
                "//input[@autocomplete='email']",
            ]

            for i, selector in enumerate(email_input_selectors, 1):
                try:
                    self.logger.debug(f"Checking email input selector {i}: {selector}")

                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        try:
                            element = self.browser.find_xpath(selector)
                        except:
                            element = None

                    if element and element.is_displayed():
                        self.logger.info(f"✅ Found email input field using selector {i}: {selector}")

                        # Clear and enter the confirmation email
                        element.clear()
                        sleep(uniform(0.5, 1.0))
                        element.send_keys(confirmation_email)
                        sleep(uniform(1.0, 2.0))

                        self.logger.info(f"✅ Entered confirmation email: {confirmation_email}")

                        # Try to submit by pressing Enter or finding a submit button
                        try:
                            element.send_keys(Keys.ENTER)
                            sleep(uniform(2.0, 3.0))
                            self.logger.info("✅ Submitted email via Enter key")
                        except:
                            # Look for submit/next button
                            submit_selectors = [
                                "//button[contains(., 'Suivant')]",
                                '//*[@id="identifierNext"]/div/button',
                                "//button[contains(text(), 'Next')]",
                                "//button[contains(text(), 'Suivant')]",
                                "//button[contains(text(), 'Continue')]",
                                "//button[contains(text(), 'Continuer')]",
                                "//button[@type='submit']",
                                "//input[@type='submit']",
                            ]

                            for submit_selector in submit_selectors:
                                try:
                                    submit_btn = self.browser.find_xpath(submit_selector)
                                    if submit_btn and submit_btn.is_displayed():
                                        if hasattr(self.browser, 'human_click_element'):
                                            self.browser.human_click_element(submit_btn)
                                        else:
                                            submit_btn.click()
                                        sleep(uniform(2.0, 3.0))
                                        self.logger.info(f"✅ Clicked submit button: {submit_selector}")
                                        break
                                except:
                                    self.browser.find_xpath('//input[@type="email"]').send_keys(Keys.ENTER)

                        return True

                except Exception as e:
                    self.logger.debug(f"Error with email input selector {i}: {str(e)}")
                    continue

            # No email input field found - this might be normal if the email is pre-selected
            self.logger.info("ℹ️ No email input field found - email might be pre-selected")
            return True

        except Exception as e:
            self.logger.error(f"Error entering confirmation email: {str(e)}")
            return False



    def _wait_for_email_verification_completion(self, confirmation_email):
        """Wait for email verification to complete and provide user guidance"""
        try:
            self.logger.warning("### EMAIL VERIFICATION REQUIRED ###")
            self.logger.warning(f"📧 Please check {confirmation_email} for the verification code")
            self.logger.warning("🔍 Look for an email from Google with a verification code")
            self.logger.warning("📝 Enter the code when prompted, or click the verification link in the email")

            # Update email status to indicate email verification is required
            self.update_email_status(self.browser.email, "email_verification_required")

            # Wait for verification completion with timeout
            max_wait = 300  # 5 minutes timeout
            wait_time = 0
            check_interval = 5  # Check every 5 seconds

            while wait_time < max_wait:
                current_url = self.browser.this_url()

                # Check if we're past the verification screen
                verification_patterns = [
                    "signin/challenge/iap",
                    "signin/challenge/ipp",
                    "signin/v2/challenge",
                    "signin/challenge"
                ]

                if not any(pattern in current_url for pattern in verification_patterns):
                    self.logger.info("✅ Email verification appears to be completed")
                    self.update_email_status(self.browser.email, "active")
                    return True

                # Check for success indicators
                success_indicators = [
                    "myaccount.google.com",
                    "accounts.google.com/ManageAccount",
                    "mail.google.com",
                    "accounts.google.com/b/0/ManageAccount"
                ]

                if any(indicator in current_url for indicator in success_indicators):
                    self.logger.info("✅ Successfully completed email verification")
                    self.update_email_status(self.browser.email, "active")
                    return True

                # Check for error indicators
                error_indicators = [
                    "signin/rejected",
                    "signin/v2/disabled",
                    "accounts.google.com/signin/v2/disabled"
                ]

                if any(indicator in current_url for indicator in error_indicators):
                    self.logger.error("❌ Email verification failed - account may be disabled")
                    self.update_email_status(self.browser.email, "disabled")
                    return False

                # Provide periodic reminders
                if wait_time % 60 == 0 and wait_time > 0:  # Every minute
                    remaining_time = (max_wait - wait_time) // 60
                    self.logger.warning(f"⏰ Still waiting for email verification... {remaining_time} minutes remaining")
                    self.logger.warning(f"📧 Please check {confirmation_email} for the verification code")

                sleep(check_interval)
                wait_time += check_interval

            # Timeout reached
            self.logger.warning("⏰ Email verification timeout reached")
            self.logger.warning("🔄 You may need to manually complete the verification or try again")
            return True  # Return True to continue, as manual intervention might have completed it

        except Exception as e:
            self.logger.error(f"Error waiting for email verification completion: {str(e)}")
            return True  # Return True to continue despite error



    def _detect_two_factor_auth(self):
        """Detect 2FA/MFA challenges"""
        try:
            current_url = self.browser.this_url()

            # Check URL patterns for 2FA
            tfa_patterns = [
                "signin/v2/challenge/totp",
                "signin/challenge/totp",
                "accounts.google.com/signin/v2/challenge/az",
                "signin/challenge/az"
            ]

            if any(pattern in current_url for pattern in tfa_patterns):
                return True

            # Check for 2FA text indicators
            tfa_indicators = [
                "Enter the code from your authenticator app",
                "2-Step Verification",
                "verification code",
                "authenticator",
                "Google Authenticator"
            ]

            for indicator in tfa_indicators:
                if self.check_js(f'"{indicator}"'):
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting 2FA: {str(e)}")
            return False




    def _handle_two_factor_auth(self):
        """Handle 2FA challenges"""
        self.logger.warning("### 2FA VERIFICATION REQUIRED ###")
        self.logger.warning("Two-factor authentication detected")
        self.update_email_status(self.browser.email, "2fa_required")

        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self.logger.warning("Please complete 2FA verification manually")

        # Wait for manual completion
        self._wait_for_verification_completion()
        return True




    def _detect_too_many_failed_attempts(self):
        """Detect 'Too many failed attempts' error and handle appropriately"""
        try:
            # Check for the specific error message
            failed_attempts_indicators = [
                "Too many failed attempts",
                "Trop de tentatives échouées",
            ]

            # Check page content for error indicators
            for indicator in failed_attempts_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.error(f"DETECTED: Too many failed attempts - {indicator}")
                    return True

            # Check for specific UI elements that indicate this error
            error_selectors = [
                '//div[contains(text(), "Too many failed attempts")]',
                '//div[contains(text(), "Unavailable because of too many failed attempts")]',
                '//div[contains(text(), "Try again in a few hours")]',
                '//div[contains(@class, "error") and contains(text(), "failed attempts")]',
                '//div[contains(@class, "warning") and contains(text(), "failed attempts")]'
            ]

            for selector in error_selectors:
                try:
                    element = self.browser.find_xpath(selector)
                    if element and element.is_displayed():
                        self.logger.error(f"DETECTED: Too many failed attempts UI element - {selector}")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.error(f"Error detecting too many failed attempts: {str(e)}")
            return False



    def _handle_too_many_failed_attempts(self):
        """Handle the 'Too many failed attempts' error by closing browser and marking account"""
        try:
            self.logger.error("### TOO MANY FAILED ATTEMPTS DETECTED ###")
            self.logger.error("Google has blocked this account due to too many failed verification attempts")

            # Update account status with timestamp
            email = self.browser.email
            if email:
                self._mark_account_failed_attempts(email)
                self.update_email_status(email, "too_many_failed_attempts")
                self.logger.error(f"Account {email} marked as 'too_many_failed_attempts' - will wait 24 hours before retry")

            # Close browser immediately to prevent further attempts
            self.logger.error("Closing browser to prevent further failed attempts...")
            self._close_browser_safely()

            return True  # Indicate that this error was handled

        except Exception as e:
            self.logger.error(f"Error handling too many failed attempts: {str(e)}")
            return False




    def _close_browser_safely(self):
        """Safely close the browser with proper cleanup"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.logger.info("Performing safe browser closure...")

                # Try to close gracefully first
                try:
                    self.browser.quit()
                    self.logger.info("Browser closed gracefully")
                except:
                    # Force close if graceful close fails
                    try:
                        self.browser.close()
                        self.logger.info("Browser force closed")
                    except:
                        self.logger.warning("Could not close browser - may need manual intervention")

        except Exception as e:
            self.logger.error(f"Error during safe browser closure: {str(e)}")




    def _mark_account_failed_attempts(self, email):
        """Mark account as having too many failed attempts with timestamp"""
        try:
            profile_details = self.get_profile_details(email)
            if profile_details is None:
                profile_details = {}

            # Record the failed attempts timestamp
            failed_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            profile_details['failed_attempts_timestamp'] = failed_timestamp
            profile_details['failed_attempts_count'] = profile_details.get('failed_attempts_count', 0) + 1
            profile_details['status'] = 'too_many_failed_attempts'

            self.update_profile_details(email, profile_details)
            self.logger.error(f"Marked account {email} with failed attempts at {failed_timestamp}")

        except Exception as e:
            self.logger.error(f"Error marking account failed attempts: {str(e)}")




    def _is_account_in_cooldown(self, email):
        """Check if account is still in 24-hour cooldown period"""
        try:
            profile_details = self.get_profile_details(email)
            if not profile_details:
                return False

            failed_timestamp_str = profile_details.get('failed_attempts_timestamp')
            if not failed_timestamp_str:
                return False

            # Parse the timestamp
            failed_timestamp = datetime.strptime(failed_timestamp_str, '%Y-%m-%d %H:%M:%S')
            current_time = datetime.now()

            # Check if 24 hours have passed
            time_diff = current_time - failed_timestamp
            hours_passed = time_diff.total_seconds() / 3600

            if hours_passed < 24:
                remaining_hours = 24 - hours_passed
                self.logger.warning(f"Account {email} is in cooldown. {remaining_hours:.1f} hours remaining")
                return True
            else:
                self.logger.info(f"Account {email} cooldown period has expired ({hours_passed:.1f} hours passed)")
                # Clear the failed attempts status
                self._clear_failed_attempts_status(email)
                return False

        except Exception as e:
            self.logger.error(f"Error checking account cooldown: {str(e)}")
            return False





    def _clear_failed_attempts_status(self, email):
        """Clear the failed attempts status after cooldown period"""
        try:
            profile_details = self.get_profile_details(email)
            if profile_details:
                # Remove failed attempts markers
                profile_details.pop('failed_attempts_timestamp', None)
                profile_details.pop('failed_attempts_count', None)
                if profile_details.get('status') == 'too_many_failed_attempts':
                    profile_details['status'] = 'null'

                self.update_profile_details(email, profile_details)
                self.logger.info(f"Cleared failed attempts status for {email}")

        except Exception as e:
            self.logger.error(f"Error clearing failed attempts status: {str(e)}")




    def _should_skip_account_due_to_cooldown(self, email):
        """Check if account should be skipped due to cooldown and log appropriate message"""
        if self._is_account_in_cooldown(email):
            self.logger.error(f"⏰ SKIPPING ACCOUNT: {email}")
            self.logger.error("❌ Account is in 24-hour cooldown due to 'too many failed attempts'")
            self.logger.error("⏳ Please wait for the cooldown period to expire before retrying")
            return True
        return False




    def check_account_status_before_verification(self, email):
        """
        Check account status before starting any verification process
        Returns True if account can proceed, False if should be skipped
        """
        try:
            # Check if account is in cooldown
            if self._is_account_in_cooldown(email):
                self.logger.error(f"❌ Account {email} is in 24-hour cooldown - skipping")
                return False

            # Check account status from profile
            profile_details = self.get_profile_details(email)
            if profile_details:
                status = profile_details.get('status', 'null')
                if status == 'too_many_failed_attempts':
                    # Double-check cooldown in case timestamp wasn't checked properly
                    if self._is_account_in_cooldown(email):
                        return False
                    else:
                        # Cooldown expired, clear status
                        self._clear_failed_attempts_status(email)

            return True

        except Exception as e:
            self.logger.error(f"Error checking account status: {str(e)}")
            return True  # Default to allowing the attempt



    def get_failed_attempts_summary(self, email):
        """Get a summary of failed attempts for an account"""
        try:
            profile_details = self.get_profile_details(email)
            if not profile_details:
                return "No failed attempts recorded"

            failed_count = profile_details.get('failed_attempts_count', 0)
            failed_timestamp = profile_details.get('failed_attempts_timestamp', 'Unknown')
            status = profile_details.get('status', 'null')

            if failed_count > 0:
                return f"Failed attempts: {failed_count}, Last failure: {failed_timestamp}, Status: {status}"
            else:
                return "No failed attempts recorded"

        except Exception as e:
            self.logger.error(f"Error getting failed attempts summary: {str(e)}")
            return "Error retrieving failed attempts data"




    def _detect_suspicious_activity(self):
        """Detect suspicious activity warnings including critical security alerts"""
                    # Check current URL for security notifications page
        current_url = self.browser.this_url()
        if "myaccount.google.com/notifications" in current_url:
            self.logger.warning("Detected security notifications page - checking for alerts")
            return True
        
        try:
            # First check for "Too many failed attempts" error
            if self._detect_too_many_failed_attempts():
                return self._handle_too_many_failed_attempts()

            # Check for critical security alert popup first (highest priority)
            critical_alert_selectors = [
                # French security alert elements (after language change)
                "//div[contains(text(), 'Alerte de sécurité critique')]",
                "//div[contains(text(), 'Activité suspecte détectée')]",
                "//div[contains(text(), 'Tentative suspecte de se connecter')]",
                "//span[contains(text(), 'Oui, c\\'était moi')]",
                "//button[.//span[contains(text(), 'Oui, c\\'était moi')]]",
                # English security alert elements
                "//div[contains(text(), 'Critical security alert')]",
                "//div[contains(text(), 'Suspicious attempt to sign in')]",
                "//button[contains(text(), 'Check activity')]",
                "//button[contains(text(), 'Yes, it was me')]",
                # Generic security alert indicators
                "//div[contains(@class, 'security-alert')]",
                "//div[contains(@class, 'critical-alert')]",
                "//div[contains(@role, 'alert')]",
                # Specific selectors from your HTML
                "//div[@class='VXxIHd' and contains(text(), 'Alerte de sécurité critique')]",
                "//div[@class='I7nwS' and contains(text(), 'Activité suspecte détectée')]"
            ]

            for selector in critical_alert_selectors:
                try:
                    # Use silent detection if enhanced driver is available
                    if self._enhanced_driver and hasattr(self._enhanced_driver, 'find_xpath_silent'):
                        element = self._enhanced_driver.find_xpath_silent(selector)
                    else:
                        # Fallback to regular find with try/catch
                        try:
                            element = self.browser.find_xpath(selector)
                        except:
                            element = None

                    if element and element.is_displayed():
                        self.logger.warning(f"Critical security alert detected via selector: {selector}")
                        return True
                except Exception as e:
                    self.logger.debug(f"Error checking security alert selector {selector}: {str(e)}")
                    continue

            # Check for text-based indicators in page content
            suspicious_indicators = [
                # French indicators (after language change)
                "Alerte de sécurité critique",
                "Activité suspecte détectée",
                "Tentative suspecte de se connecter",
                "activité suspecte",
                "activité inhabituelle",
                "Vous voyez une activité inhabituelle",
                "Reconnaissez-vous cette activité",
                # English indicators
                "Critical security alert",
                "Suspicious attempt to sign in",
                "suspicious activity",
                "unusual activity",
                "We noticed something unusual",
                "Check activity",
                "Do you recognize this activity"
            ]

            for indicator in suspicious_indicators:
                if self.check_js(f'"{indicator}"'):
                    self.logger.warning(f"Suspicious activity detected via text: {indicator}")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting suspicious activity: {str(e)}")
            return False
        



    def _handle_suspicious_activity(self):
        """Handle suspicious activity warnings including critical security alerts"""
        self.update_email_status(self.browser.email, "suspicious_activity")

        # Check if we've already cleared suspicious activity for this account
        email = self.browser.email
        if email and self._is_suspicious_activity_cleared(email):
            self.logger.info(f"Suspicious activity already cleared for {email}, attempting to continue")
            return False
        
        self.logger.info("Handling security notifications page")
        if self._check_and_clear_security_notifications():
            self.logger.info("Security notifications page handled successfully")
            return False
        else:
            self.logger.warning("Could not handle security notifications page")

        return True


    def _handle_detailed_security_activity_page(self):
        """
        Handle the detailed security activity page that appears after clicking on a specific alert.
        This page shows details about the suspicious activity and asks for confirmation.
        """
        try:
            self.logger.info("Handling detailed security activity page...")

            # Wait for detailed page to load
            sleep(uniform(0.5, 1.0))

            # Look for the main confirmation buttons on the detailed page
            confirmation_buttons = [
                '//*[@id="yDmH0d"]/c-wiz/div/div[2]/div[2]/c-wiz/div/div/div/div[2]/div[2]/div/div[4]/div[2]/div/button',
                '//*[@id="yDmH0d"]/c-wiz/div/div[2]/div[2]/c-wiz/div/div/div[2]/div/div[2]/div/div[5]/button[2]',
                '(//button)[8]'
            ]
            button_clicked = False
            for button_selector in confirmation_buttons:
                try:
                    button = self.browser.find_xpath(button_selector)
                    if button and button.is_displayed():
                        self.logger.info(f"Found detailed page confirmation button: {button_selector}")

                        # Get button details for logging
                        button_text = button.text.strip()
                        self.logger.info(f"Button text: '{button_text}'")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                            button_clicked = True
                        else:
                            button.click()
                            button_clicked = True

                        sleep(uniform( 0.3, 0.5))
                        self.logger.info("Clicked confirmation button on detailed security page")


                        return True
                except Exception as e:
                    self.logger.debug(f"Confirmation button {button_selector} failed")
                    continue

            if button_clicked is False:
                try:
                    self.browser.execute_js(""" Array.from(document.querySelectorAll('button')).find(btn => 
                        btn.innerHTML.includes('c\'était moi') || btn.textContent.includes('c\'était moi')
                    ).click() """)
                except Exception as e:
                    self.logger.debug(f"JavaScript confirmation button click failed.")

        except Exception as e:
            self.logger.error(f"Error handling detailed security activity page: {str(e)}")
            return False


    def _check_and_clear_security_notifications(self):
        """
        Proactively visit the Google Account security notifications page to clear any suspicious activity alerts.
        This should be called after language change to prevent security popups during login.
        Note: This always checks the page - suspicious activity can occur at any time regardless of previous clearings.
        """
        try:
            email = self.browser.email

            self.logger.info("Proactively checking Google Account security notifications...")

            # Navigate to the security notifications page
            notifications_url = "https://myaccount.google.com/notifications"
            self.browser.go(notifications_url)

            # Wait for page to load
            sleep(uniform(1.0, 2.0))

            # Check if we successfully reached the notifications page
            current_url = self.browser.this_url()
            if "notifications" not in current_url:
                self.logger.warning(f"Failed to reach notifications page, current URL: {current_url}")
                return False

            self.logger.info("Successfully reached security notifications page")

            # REMOVED: Problematic "Vous voyez une activité inhabituelle?" button detection
            # This button incorrectly triggers password change flows instead of handling suspicious activity
            unusual_activity_handled = False
            self.logger.info("Skipping 'unusual activity' button detection to avoid password change flows")

            # Look for and handle any critical security alerts
            # Use the correct XPath selector and implement notification ID tracking
            critical_alerts_handled = 0
            critical_alert_selectors = [
                # Primary selector that successfully finds actual suspicious activity notifications
                '//li[@class="Tti8Vd VfPpkd-ksKsZd-XxIAqe"]//a[contains(@href, "notifications") and .//span[contains(@class, "Xc5Wg") and text()="Nouveau"]]',
                # French critical security alerts
                #'//a[contains(@href, "notifications/eid/") and .//div[contains(text(), "Activité suspecte détectée")]]',
                #'//a[@jsname="cDqwkb" and .//div[contains(text(), "Activité suspecte")]]',
                # English equivalents
                #'//a[contains(@href, "notifications/eid/") and .//div[contains(text(), "Suspicious activity detected")]]'
            ]

            for alert_selector in critical_alert_selectors:
                try:
                    try:
                        alerts = self.browser.find_xpath_all(alert_selector)
                        print(len(alerts))
                    except:
                        continue
                    for alert in alerts:
                        if alert:
                            print(alert)
                            # Extract notification ID from the link href
                            href = alert.get_attribute('href')
                            notification_id = self._extract_notification_id_from_url(href) if href else None

                            # Skip if this notification has already been processed
                            if notification_id and self._is_notification_processed(self.browser.email, notification_id):
                                self.logger.info(f"Skipping already processed notification ID: {notification_id}")
                                continue

                            self.logger.info(f"Found new critical security alert: {alert_selector}, notification ID: {notification_id}")

                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(alert)
                            else:
                                alert.click()

                            sleep(uniform(0.5, 1.0))

                            # Handle the detailed security page that opens
                            if self._handle_detailed_security_activity_page():
                                critical_alerts_handled += 1
                                self.logger.info(f"Successfully handled critical security alert #{critical_alerts_handled}")

                                # Mark this notification as processed
                                if notification_id:
                                    self._add_processed_notification_id(self.browser.email, notification_id)

                                # Navigate back to notifications page for any remaining alerts
                                self.browser.go(notifications_url)
                                sleep(uniform(1.0, 1.5))
                            else:
                                self.logger.warning("Failed to handle detailed security activity page")

                except Exception as e:
                    self.logger.debug(f"Alert selector {alert_selector} failed: {str(e)}")
                    continue


            # Summary of actions taken
            total_actions = (1 if unusual_activity_handled else 0) + critical_alerts_handled
            self.logger.info(f"Security notifications check completed:")
            self.logger.info(f"  - Unusual activity button: {'✓' if unusual_activity_handled else '✗'}")
            self.logger.info(f"  - Critical alerts handled: {critical_alerts_handled}")
            self.logger.info(f"  - Total actions taken: {total_actions}")

            # Only mark suspicious activity as cleared if we actually found and handled suspicious activity
            if total_actions > 0:
                self.logger.info("Suspicious activity was found and handled, marking as cleared")
                self._mark_suspicious_activity_cleared()
            else:
                self.logger.info("No suspicious activity found on security notifications page")

            # Wait a bit before returning
            sleep(uniform(2.0, 3.0))

            return True  # Always return True - we successfully checked the page

        except Exception as e:
            self.logger.error(f"Error checking and clearing security notifications: {str(e)}")
            return False

    def _extract_notification_id_from_url(self, url):
        """
        Extract notification ID from Google notifications URL

        Args:
            url (str): URL containing notification ID (e.g., https://myaccount.google.com/notifications/eid/-8863226562822209640)

        Returns:
            str: Notification ID or None if not found
        """
        try:
            import re
            # Match pattern: /notifications/eid/[ID] where ID can be negative or positive
            match = re.search(r'/notifications/eid/(-?\d+)', url)
            if match:
                notification_id = match.group(1)
                self.logger.debug(f"Extracted notification ID: {notification_id} from URL: {url}")
                return notification_id
            return None
        except Exception as e:
            self.logger.error(f"Error extracting notification ID from URL {url}: {str(e)}")
            return None

    def _get_processed_notification_ids(self, email):
        """
        Get list of processed notification IDs for an account

        Args:
            email (str): Account email

        Returns:
            list: List of processed notification IDs
        """
        try:
            profile_details = self.get_profile_details(email)
            if profile_details:
                return profile_details.get('processed_notification_ids', [])
            return []
        except Exception as e:
            self.logger.error(f"Error getting processed notification IDs for {email}: {str(e)}")
            return []

    def _add_processed_notification_id(self, email, notification_id):
        """
        Add a notification ID to the processed list for an account

        Args:
            email (str): Account email
            notification_id (str): Notification ID to mark as processed
        """
        try:
            profile_details = self.get_profile_details(email)
            if profile_details is None:
                profile_details = {}

            processed_ids = profile_details.get('processed_notification_ids', [])
            if notification_id not in processed_ids:
                processed_ids.append(notification_id)
                profile_details['processed_notification_ids'] = processed_ids
                profile_details['last_notification_processed'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.update_profile_details(email, profile_details)
                self.logger.info(f"Added notification ID {notification_id} to processed list for {email}")
            else:
                self.logger.debug(f"Notification ID {notification_id} already processed for {email}")
        except Exception as e:
            self.logger.error(f"Error adding processed notification ID for {email}: {str(e)}")

    def _is_notification_processed(self, email, notification_id):
        """
        Check if a notification ID has already been processed for an account

        Args:
            email (str): Account email
            notification_id (str): Notification ID to check

        Returns:
            bool: True if already processed, False otherwise
        """
        try:
            processed_ids = self._get_processed_notification_ids(email)
            is_processed = notification_id in processed_ids
            if is_processed:
                self.logger.info(f"Notification ID {notification_id} already processed for {email}")
            return is_processed
        except Exception as e:
            self.logger.error(f"Error checking if notification processed for {email}: {str(e)}")
            return False

    def _mark_suspicious_activity_cleared(self):
        """Mark that suspicious activity has been cleared for this account"""
        try:
            email = self.browser.email
            if not email:
                self.logger.warning("No email available to mark suspicious activity cleared")
                return

            # Update profile details to include suspicious activity cleared status
            profile_details = self.get_profile_details(email)
            if profile_details:
                profile_details['suspicious_activity_cleared'] = True
                profile_details['suspicious_activity_cleared_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.update_profile_details(email, profile_details)
                self.logger.info(f"Marked suspicious activity as cleared for {email}")
            else:
                self.logger.warning(f"Could not get profile details to mark suspicious activity cleared for {email}")

        except Exception as e:
            self.logger.error(f"Error marking suspicious activity cleared: {str(e)}")

    def _is_suspicious_activity_cleared(self, email, check_recent=False):
        """
        Check if suspicious activity has already been cleared for this account

        Args:
            email: The email address to check
            check_recent: If True, only consider it cleared if it was within the last 24 hours
        """
        try:
            profile_details = self.get_profile_details(email)
            if profile_details:
                cleared = profile_details.get('suspicious_activity_cleared', False)
                if cleared:
                    cleared_date_str = profile_details.get('suspicious_activity_cleared_date', 'Unknown')

                    if check_recent and cleared_date_str != 'Unknown':
                        try:
                            cleared_date = datetime.strptime(cleared_date_str, '%Y-%m-%d %H:%M:%S')
                            hours_since_cleared = (datetime.now() - cleared_date).total_seconds() / 3600

                            if hours_since_cleared > 24:  # More than 24 hours ago
                                self.logger.info(f"Suspicious activity was cleared {hours_since_cleared:.1f} hours ago for {email}, may need re-checking")
                                return False
                        except:
                            # If we can't parse the date, assume it needs re-checking
                            return False

                    self.logger.info(f"Suspicious activity cleared for {email} on {cleared_date_str}")
                    return True
            return False
        except Exception as e:
            self.logger.error(f"Error checking suspicious activity cleared status: {str(e)}")
            return False

    def get_profile_details(self, email):
        """
        Get profile details for an email account

        Args:
            email (str): Email address

        Returns:
            dict: Profile details or None if not found
        """
        try:
            # Try to get from enhanced profile manager first
            if hasattr(self, 'browser') and hasattr(self.browser, 'profile_manager'):
                profile_id = self.browser.profile_manager._generate_profile_id(email)
                if profile_id in self.browser.profile_manager.profiles_config:
                    profile = self.browser.profile_manager.profiles_config[profile_id]
                    # Return account_settings as profile details, or create empty dict
                    return profile.get('account_settings', {})

            # Fallback: try to load from a simple JSON file
            profile_details_file = f"{profile_home}/{email}_details.json"
            if os.path.exists(profile_details_file):
                with open(profile_details_file, 'r') as f:
                    return json.load(f)

            # Return empty dict if no profile details found
            return {}

        except Exception as e:
            self.logger.error(f"Error getting profile details for {email}: {str(e)}")
            return None

    def update_profile_details(self, email, profile_details):
        """
        Update profile details for an email account

        Args:
            email (str): Email address
            profile_details (dict): Profile details to save
        """
        try:
            # Try to update enhanced profile manager first
            if hasattr(self, 'browser') and hasattr(self.browser, 'profile_manager'):
                profile_id = self.browser.profile_manager._generate_profile_id(email)
                if profile_id in self.browser.profile_manager.profiles_config:
                    profile = self.browser.profile_manager.profiles_config[profile_id]
                    profile['account_settings'] = profile_details
                    profile['last_updated'] = datetime.now().isoformat()
                    self.browser.profile_manager._save_profiles_config()
                    self.logger.info(f"Updated profile details via ProfileManager for {email}")
                    return

            # Fallback: save to a simple JSON file
            profile_details_file = f"{profile_home}/{email}_details.json"
            os.makedirs(os.path.dirname(profile_details_file), exist_ok=True)

            with open(profile_details_file, 'w') as f:
                json.dump(profile_details, f, indent=4)

            self.logger.info(f"Updated profile details via JSON file for {email}")

        except Exception as e:
            self.logger.error(f"Error updating profile details for {email}: {str(e)}")




    def _should_change_language_to_french(self):
        """
        Check if we should change the account language to French.
        Returns True if this is the first login or language hasn't been changed yet.
        """
        try:
            # Check if language_changed flag exists in Gmail accounts map
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for item in data:
                if item['email'] == self.browser.email:
                    # If language_changed field doesn't exist or is False, we should change it
                    language_changed = item.get('language_changed', False)
                    self.logger.info(f"Language change status for {self.browser.email}: {language_changed}")
                    return not language_changed

            # If account not found in map, assume first login
            self.logger.info(f"Account {self.browser.email} not found in map, assuming first login")
            return True

        except Exception as e:
            self.logger.error(f"Error checking language change status: {str(e)}")
            # Default to NOT changing language if we can't determine status to avoid repeated attempts
            return False




    def _update_language_changed_status(self, email, status=True):
        """
        Update the language_changed status in the Gmail accounts map.

        Args:
            email (str): Email address
            status (bool): Language changed status (default: True)
        """
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            # Find and update the account
            for item in data:
                if item['email'] == email:
                    item['language_changed'] = status
                    break
            else:
                # Account not found, add it
                data.append({
                    'email': email,
                    'password': '',  # Will be updated elsewhere
                    'ua': '',
                    'email_conf': '',
                    'phone': '',
                    'status': 'active',
                    'language_changed': status
                })

            # Save updated data
            with open(gmail_map_file, 'w') as f:
                json.dump(data, f, indent=4)

            self.logger.info(f"Updated language_changed status for {email}: {status}")

            # Also update profile configuration if enhanced driver is available
            try:
                if hasattr(self, 'browser') and hasattr(self.browser, 'profile_manager'):
                    profile_id = self.browser.profile_manager._generate_profile_id(email)
                    if profile_id in self.browser.profile_manager.profiles_config:
                        profile = self.browser.profile_manager.profiles_config[profile_id]
                        if 'account_settings' not in profile:
                            profile['account_settings'] = {}
                        profile['account_settings']['language_changed_to_french'] = status
                        profile['account_settings']['language_change_date'] = datetime.now().isoformat()
                        self.browser.profile_manager._save_profiles_config()
                        self.logger.info(f"Updated profile configuration for language change: {email}")
            except Exception as profile_error:
                self.logger.debug(f"Could not update profile configuration: {str(profile_error)}")

        except Exception as e:
            self.logger.error(f"Error updating language changed status: {str(e)}")



    def _generate_strong_password(self, length=12):
        """Generate a strong password compatible with Gmail requirements"""
        try:
            # Gmail password requirements: letters, numbers, symbols, 8+ characters
            # Use a mix of uppercase, lowercase, numbers, and safe symbols
            safe_symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"

            # Ensure at least one of each type
            password_chars = [
                secrets.choice(ascii_letters.upper()),  # At least one uppercase
                secrets.choice(ascii_letters.lower()),  # At least one lowercase
                secrets.choice(digits),                 # At least one digit
                secrets.choice(safe_symbols)            # At least one symbol
            ]

            # Fill the rest randomly
            all_chars = ascii_letters + digits + safe_symbols
            for _ in range(length - 4):
                password_chars.append(secrets.choice(all_chars))

            # Shuffle to avoid predictable patterns
            secrets.SystemRandom().shuffle(password_chars)

            password = ''.join(password_chars)
            self.logger.info(f"Generated strong password of length {len(password)}")
            return password

        except Exception as e:
            self.logger.error(f"Error generating password: {str(e)}")
            # Fallback to a simple but strong password
            return f"Gm{secrets.randbelow(9999):04d}!{secrets.choice(ascii_letters.upper())}"
        


    def _is_password_updated(self, email):
        """Check if password has already been updated for this account"""
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for item in data:
                if item['email'] == email:
                    return item.get('password_updated', False)
            return False
        except Exception as e:
            self.logger.error(f"Error checking password update status: {str(e)}")
            return False
        

    
    def _update_password_status(self, email, status=True):
        """Update the password updated status and change the password"""
        try:
            self.logger.info("Starting password update process...")

            # Navigate to password sign-in options page
            self.logger.info("Navigating to password sign-in options page...")
            self.browser.go("https://myaccount.google.com/signinoptions/password")
            sleep(uniform(2.0, 3.0))

            # Check if redirected to signin page - if so, enter existing password
            current_url = self.browser.this_url()
            print(current_url)
            if "https://accounts.google.com" in current_url and "https://myaccount.google.com/signinoptions/password" not in current_url:
                self.logger.info("Redirected to password page, entering password...")
                try:
                    # Find password input field
                    password_input = self.browser.find_xpath("//input[@type='password']")
                    if password_input:
                        password_input.clear()
                        # Use proper human-like typing method
                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(password_input, self.browser.password)
                        else:
                            password_input.send_keys(self.browser.password)
                        sleep(uniform(0.5, 0.7))

                        # Click continue/next button
                        continue_button = self.browser.find_xpath("//button[contains(., 'Next') or contains(., 'Suivant')]")
                        if continue_button:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(continue_button)
                            else:
                                continue_button.click()
                            sleep(uniform(1.0, 1.5))
                except Exception as e:
                    self.logger.warning(f"Error entering password")

            # Wait and verify we're on the password change page
            sleep(uniform(0.5, 1.3))
            current_url = self.browser.this_url()

            if "https://myaccount.google.com/signinoptions/password" not in current_url:
                self.logger.error(f"Not on password change page. Current URL: {current_url}")
                return False

            self.logger.info("Successfully reached password change page")

            # Generate a strong password
            new_password = self._generate_strong_password()
            self.logger.info("Generated new strong password")

            # Fill password fields
            try:
                # First password input
                self.logger.info("Filling first password field...")
                # Wait for first password field to be present
                self.browser.wait_xpath_presence("(//input[@type='password'])[1]", timeout=10)
                first_password_field = self.browser.find_xpath("(//input[@type='password'])[1]")

                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(first_password_field, new_password)
                else:
                    first_password_field.clear()
                    first_password_field.send_keys(new_password)

                sleep(uniform(0.5, 1.0))

                # Confirmation password
                self.logger.info("Filling confirmation password field...")

                # Wait for confirmation password field to be present
                self.browser.wait_xpath_presence("(//input[@type='password'])[2]", timeout=10)
                confirm_password_field = self.browser.find_xpath("(//input[@type='password'])[2]")

                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(confirm_password_field, new_password)
                else:
                    confirm_password_field.clear()
                    confirm_password_field.send_keys(new_password)

                self.logger.info("Successfully filled both password fields")

            except Exception as e:
                self.logger.error(f"Error filling password fields: {str(e)}")
                return False

            sleep(uniform(1.0, 2.0))

            # Submit the password change
            try:
                self.logger.info("Attempting to submit password change...")

                # Try multiple button selectors
                button_selectors = [
                    '//button[contains(., "Modifier le mot de passe")]',
                    '//button[contains(@class, "UywwFc-LgbsSe") and contains(., "Modifier le mot de passe")]',
                    '//button//span[@class="UywwFc-vQzf8d" and text()="Modifier le mot de passe"]'
                ]

                button_clicked = False
                for selector in button_selectors:
                    try:
                        submit_button = self.browser.find_xpath(selector)
                        if submit_button:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(submit_button)
                            else:
                                submit_button.click()
                            self.logger.info(f"Clicked submit button using selector: {selector}")
                            button_clicked = True
                            break
                    except:
                        continue

                if not button_clicked:
                    self.logger.error("Could not find or click submit button")
                    return False

            except Exception as e:
                self.logger.error(f"Error submitting password change: {str(e)}")
                return False

            # Wait for submission to complete
            sleep(1.0)

            # Update GmailAccountsMap.json
            try:
                self.logger.info("Updating GmailAccountsMap.json...")

                with open(gmail_map_file, 'r') as f:
                    data = json.load(f)

                # Find and update the account
                account_found = False
                for item in data:
                    if item['email'] == email:
                        # Move current password to old_password field
                        item['old_password'] = item.get('password', '')
                        # Set new password
                        item['password'] = new_password
                        # Set password_updated flag
                        item['password_updated'] = True
                        item['password_update_date'] = datetime.now().isoformat()
                        account_found = True
                        break

                if not account_found:
                    self.logger.warning(f"Account {email} not found in GmailAccountsMap.json")
                    return False

                # Save updated data
                with open(gmail_map_file, 'w') as f:
                    json.dump(data, f, indent=4)

                self.logger.info(f"Updated password in Gmail map for {email}")

                # Update browser password attribute for current session
                if hasattr(self.browser, 'password'):
                    self.browser.password = new_password
                    self.logger.info("Updated browser password attribute")

            except Exception as e:
                self.logger.error(f"Error updating GmailAccountsMap.json: {str(e)}")
                return False

            # Update profile manager if available
            try:
                if hasattr(self.browser, 'profile_manager') and self.browser.profile_manager:
                    profile = self.browser.profile_manager.get_profile(email)
                    if profile:
                        profile['password_updated'] = True
                        profile['password_update_date'] = datetime.now().isoformat()
                        self.browser.profile_manager._save_profiles_config()
                        self.logger.info("Updated profile manager with password status")
            except Exception as e:
                self.logger.warning(f"Could not update profile manager: {str(e)}")

            self.logger.info("Password update completed successfully!")
            return True

        except Exception as e:
            self.logger.error(f"Error during password update: {str(e)}")
            return False


    def _is_2fa_enabled(self, email):
        """Check if 2FA has already been enabled for this account"""
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for item in data:
                if item['email'] == email:
                    return item.get('2fa_enabled', False)
            return False
        except Exception as e:
            self.logger.error(f"Error checking 2FA status: {str(e)}")
            return False
        

    def _enable_2fa_automatically(self):
        """Enable 2FA automatically after login"""
        try:
            self.logger.info("Starting automatic 2FA setup...")

            # Navigate to 2FA sign-in options page
            self.logger.info("Navigating to 2FA sign-in options page...")
            self.browser.go("https://myaccount.google.com/signinoptions/twosv")
            sleep(uniform(2.0, 3.0))

            # Check if we've been redirected to password page
            current_url = self.browser.this_url()
            if "password" in current_url.lower() or "signin" in current_url:
                self.logger.info("Redirected to password page, entering password...")
                try:
                    # Find password input field
                    password_input = self.browser.find_xpath("//input[@type='password']")
                    if password_input:
                        password_input.clear()
                        # Use proper human-like typing method
                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(password_input, self.browser.password)
                        else:
                            password_input.send_keys(self.browser.password)
                        sleep(uniform(1.0, 2.0))

                        # Click continue/next button
                        continue_button = self.browser.find_xpath("//button[contains(., 'Next') or contains(., 'Suivant')]")
                        if continue_button:
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(continue_button)
                            else:
                                continue_button.click()
                            sleep(uniform(2.0, 3.0))
                except Exception as e:
                    self.logger.warning(f"Error entering password")

            # Navigate to authenticator setup page
            self.logger.info("Navigating to authenticator setup page...")
            self.browser.go("https://myaccount.google.com/two-step-verification/authenticator")
            sleep(uniform(2.0, 3.0))

            # Try to click on "Configurer une appli d'authentification" button
            setup_button_xpaths = [
                "//button[contains(text(), 'Set up authenticator') or contains(text(), 'Configurer une appli') or .//span[contains(text(), 'Set up authenticator')] or .//span[contains(text(), 'Configurer une appli')]]",
                "//button[contains(@class, 'AeBiU-LgbsSe') and @jscontroller='O626Fe']",
                "//button[.//*[text()='Configurer une appli d\\'authentification']]"
            ]

            button_clicked = False
            for xpath in setup_button_xpaths:
                try:
                    button = self.browser.find_xpath(xpath)
                    if button and button.is_displayed():
                        self.logger.info(f"Found setup button with xpath: {xpath}")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()
                        button_clicked = True
                        break
                except Exception as e:
                    self.logger.debug(f"Setup button xpath failed: {xpath} - {str(e)}")
                    continue

            if not button_clicked:
                self.logger.error("Could not find or click setup authenticator button")
                return False

            # Wait for next page to load
            sleep(uniform(1.5, 2.5))

            # Wait for "Suivant" or "Annuler" button to appear
            next_button_xpaths = [
                "(//button[.//span[text()='Next'] or .//span[text()='Suivant']])[2]"
            ]

            for xpath in next_button_xpaths:
                try:
                    button = self.browser.find_xpath(xpath)
                    if button and button.is_displayed():
                        self.logger.info(f"Found next/cancel button: {xpath}")
                        break
                except:
                    continue

            # Extract QR code image source
            self.logger.info("Extracting QR code image...")
            qr_image_xpaths = [
                "(//div[@jsname='dj7gwc']//img[@class='iLCDJd'])[2]",
                "//img[@class='iLCDJd']"
            ]

            qr_image_src = None
            for xpath in qr_image_xpaths:
                try:
                    qr_img = self.browser.find_xpath(xpath)
                    print(qr_img)
                    if qr_img:
                        qr_image_src = qr_img.get_attribute("src")
                        print(qr_image_src)
                        if qr_image_src:
                            self.logger.info(f"Found QR code image with xpath: {xpath}")
                            break
                except Exception as e:
                    self.logger.debug(f"QR image xpath failed: {xpath} - {str(e)}")
                    continue

            if not qr_image_src:
                self.logger.error("Could not extract QR code image source")
                return False

            # Extract secret from QR code and store in 2FA manager
            self.logger.info("Processing QR code and storing 2FA secret...")
            success = False

            try:
                # Method 1: Use enhanced driver's screenshot-based QR scanning
                if hasattr(self._enhanced_driver, 'scan_2fa_qr_from_screenshot'):
                    self.logger.info("Using enhanced driver's screenshot-based QR scanning...")
                    success = self._enhanced_driver.scan_2fa_qr_from_screenshot(service_name="Google")
                    if success:
                        self.logger.info("QR code scanned and stored successfully via screenshot method")

                # Method 2: Direct TwoFAManager usage with screenshot
                if not success and hasattr(self._enhanced_driver, 'twofa_manager') and self._enhanced_driver.twofa_manager:
                    self.logger.info("Using TwoFAManager with screenshot data...")
                    try:
                        # Take screenshot using correct SeleniumBase method
                        screenshot_data = None

                        # Try different screenshot methods
                        if hasattr(self.browser, 'get_screenshot_as_png'):
                            screenshot_data = self.browser.get_screenshot_as_png()
                        elif hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'get_screenshot_as_png'):
                            screenshot_data = self.browser.driver.get_screenshot_as_png()
                        elif hasattr(self.browser, 'save_screenshot'):
                            # Fallback: save to temp file and read
                            import tempfile
                            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                                temp_path = temp_file.name
                            self.browser.save_screenshot(temp_path)
                            with open(temp_path, 'rb') as f:
                                screenshot_data = f.read()
                            os.unlink(temp_path)

                        if screenshot_data:
                            # Generate profile identifier
                            profile_identifier = f"{self.browser.email.split('@')[0]}_{getattr(self._enhanced_driver, 'index', 0)}"

                            # Scan QR code from screenshot
                            qr_info = self._enhanced_driver.twofa_manager.scan_qr_code_from_screenshot(screenshot_data)

                            if qr_info:
                                # Store the secret
                                success = self._enhanced_driver.twofa_manager.store_2fa_secret(
                                    profile_identifier=profile_identifier,
                                    service_name="Google",
                                    secret=qr_info['secret'],
                                    issuer=qr_info.get('issuer', 'Google'),
                                    account_name=qr_info.get('account_name', self.browser.email)
                                )
                                if success:
                                    self.logger.info("✅ QR code scanned and stored successfully via TwoFAManager")
                            else:
                                self.logger.warning("No QR code found in screenshot")
                        else:
                            self.logger.warning("Could not take screenshot")
                    except Exception as e:
                        self.logger.warning(f"TwoFAManager screenshot method failed: {str(e)}")

                # Method 3: Process QR image (base64 or URL) with TwoFAManager
                if not success and qr_image_src and hasattr(self._enhanced_driver, 'twofa_manager') and self._enhanced_driver.twofa_manager:
                    self.logger.info("Processing QR image with TwoFAManager...")
                    try:
                        import tempfile
                        import base64

                        temp_file_path = None

                        # Check if it's a base64 data URL
                        if qr_image_src.startswith('data:image/'):
                            self.logger.info("Processing base64 data URL...")
                            try:
                                # Extract base64 data after the comma
                                _, encoded = qr_image_src.split(',', 1)
                                # Decode base64 data
                                image_data = base64.b64decode(encoded)

                                # Save to temporary file
                                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                                    temp_file.write(image_data)
                                    temp_file_path = temp_file.name

                                self.logger.info("Base64 image decoded and saved successfully")
                            except Exception as e:
                                self.logger.warning(f"Failed to decode base64 image: {str(e)}")

                        # If not base64, try downloading as regular URL
                        elif qr_image_src.startswith(('http://', 'https://')):
                            self.logger.info("Downloading QR image from URL...")
                            try:
                                import requests
                                response = requests.get(qr_image_src)
                                if response.status_code == 200:
                                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                                        temp_file.write(response.content)
                                        temp_file_path = temp_file.name
                                    self.logger.info("QR image downloaded successfully")
                                else:
                                    self.logger.warning(f"Failed to download QR image: HTTP {response.status_code}")
                            except Exception as e:
                                self.logger.warning(f"Failed to download QR image: {str(e)}")

                        # Process the image file if we have one
                        if temp_file_path:
                            # Generate profile identifier
                            profile_identifier = f"{self.browser.email.split('@')[0]}_{getattr(self._enhanced_driver, 'index', 0)}"

                            # Use TwoFAManager to scan and store
                            success = self._enhanced_driver.twofa_manager.store_from_qr_code(
                                profile_identifier=profile_identifier,
                                image_path=temp_file_path,
                                service_name="Google"
                            )

                            # Clean up temp file
                            os.unlink(temp_file_path)

                            if success:
                                self.logger.info("QR code processed and stored successfully via TwoFAManager")
                        else:
                            self.logger.warning("Could not process QR image - no valid image data")

                    except Exception as e:
                        self.logger.warning(f"QR image processing method failed: {str(e)}")

                # Method 4: Fallback to legacy browser methods
                if not success and qr_image_src:
                    self.logger.info("Trying legacy browser methods as fallback...")
                    if hasattr(self.browser, 'scan_and_store_2fa_qr'):
                        try:
                            import tempfile
                            import base64
                            temp_file_path = None

                            # Handle base64 or regular URL
                            if qr_image_src.startswith('data:image/'):
                                # Process base64 data URL
                                _, encoded = qr_image_src.split(',', 1)
                                image_data = base64.b64decode(encoded)
                                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                                    temp_file.write(image_data)
                                    temp_file_path = temp_file.name
                            elif qr_image_src.startswith(('http://', 'https://')):
                                # Download from URL
                                import requests
                                response = requests.get(qr_image_src)
                                if response.status_code == 200:
                                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                                        temp_file.write(response.content)
                                        temp_file_path = temp_file.name

                            if temp_file_path:
                                success = self.browser.scan_and_store_2fa_qr(temp_file_path, "Google")
                                os.unlink(temp_file_path)  # Clean up temp file
                                if success:
                                    self.logger.info("QR code processed successfully via legacy browser method")
                        except Exception as e:
                            self.logger.warning(f"Legacy browser method failed: {str(e)}")

                if not success:
                    self.logger.error("All QR code processing methods failed")
                    return False

            except Exception as e:
                self.logger.error(f"Error processing QR code: {str(e)}")
                return False

            # Click "Suivant" button to proceed
            next_button_xpaths = [
                "(//button[.//span[text()='Next'] or .//span[text()='Suivant']])[2]"
            ]

            button_clicked = False
            for xpath in next_button_xpaths:
                try:
                    button = self.browser.find_xpath(xpath)
                    if button and button.is_displayed():
                        self.logger.info(f"Clicking next button: {xpath}")
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()
                        button_clicked = True
                        break
                except Exception as e:
                    self.logger.debug(f"Next button xpath failed: {xpath} - {str(e)}")
                    continue

            # Try JavaScript fallback if XPath failed
            if not button_clicked:
                try:
                    self.browser.execute_js('document.querySelectorAll("button[data-id=\'OCpkoe\']")[3].click()')
                    button_clicked = True
                    self.logger.info("Clicked next button using JavaScript")
                except Exception as e:
                    self.logger.debug(f"JavaScript next button click failed: {str(e)}")

            if not button_clicked:
                self.logger.error("Could not click next button")
                return False

            sleep(uniform(0.5, 1.0))

            # Generate and enter OTP code
            self.logger.info("Generating and entering OTP code...")
            try:
                # Generate 2FA code using multiple methods
                otp_code = None

                # Method 1: Use enhanced driver's get_2fa_code method
                if hasattr(self._enhanced_driver, 'get_2fa_code'):
                    self.logger.info("Generating OTP code via enhanced driver...")
                    otp_code = self._enhanced_driver.get_2fa_code("Google")
                    if otp_code:
                        self.logger.info("OTP code generated via enhanced driver")

                # Method 2: Direct TwoFAManager usage
                if not otp_code and hasattr(self._enhanced_driver, 'twofa_manager') and self._enhanced_driver.twofa_manager:
                    self.logger.info("Generating OTP code via TwoFAManager...")
                    try:
                        # Generate profile identifier
                        profile_identifier = f"{self.browser.email.split('@')[0]}_{getattr(self._enhanced_driver, 'index', 0)}"

                        # Get 2FA code from TwoFAManager
                        otp_code = self._enhanced_driver.twofa_manager.get_2fa_code(profile_identifier, "Google")
                        if otp_code:
                            self.logger.info("OTP code generated via TwoFAManager")
                    except Exception as e:
                        self.logger.warning(f"TwoFAManager OTP generation failed: {str(e)}")

                # Method 3: Fallback to legacy browser method
                if not otp_code and hasattr(self.browser, 'get_2fa_code'):
                    self.logger.info("Generating OTP code via legacy browser method...")
                    otp_code = self.browser.get_2fa_code("Google")
                    if otp_code:
                        self.logger.info("OTP code generated via legacy browser method")

                if not otp_code:
                    self.logger.error("Could not generate OTP code using any method")
                    return False

                self.logger.info(f"Generated OTP code: {otp_code}")

                # Find OTP input field
                otp_input_xpaths = [
                    "//input[contains(@placeholder, 'code') and (contains(@placeholder, 'Saisissez') or contains(@placeholder, 'Enter') or contains(@placeholder, 'Entrez'))]",
                    "//input[@jsname='YPqjbf' and (@placeholder='Saisissez le code' or @placeholder='Enter the code')]",
                    "//input[@placeholder='Saisissez le code' or @placeholder='Enter the code']"
                ]

                input_found = False
                for xpath in otp_input_xpaths:
                    try:
                        otp_input = self.browser.find_xpath(xpath)
                        if otp_input and otp_input.is_displayed():
                            self.logger.info(f"Found OTP input field: {xpath}")
                            otp_input.clear()
                            otp_input.send_keys(otp_code)
                            input_found = True
                            break
                    except Exception as e:
                        self.logger.debug(f"OTP input xpath failed: {xpath} - {str(e)}")
                        continue

                if not input_found:
                    self.logger.error("Could not find OTP input field")
                    return False

                sleep(uniform(0.5, 1.0))

                # Click "Valider" button
                validate_button_xpaths = [
                    "(//button[(.//span[text()='Valider'] or .//span[text()='Validate'] or .//span[text()='Confirm']) or (@jsname='LgbsSe' and .//span[@jsname='V67aGc' and (text()='Valider' or text()='Validate' or text()='Confirm')])])[2]",
                    "(//button[@jsname='LgbsSe'][.//span[@jsname='V67aGc' and (text()='Valider' or text()='Validate' or text()='Confirm')]])[2]",
                    "(//button[(.//span[text()='Valider'] or .//span[text()='Validate'] or .//span[text()='Confirm']) or (@jsname='LgbsSe' and .//span[@jsname='V67aGc' and (text()='Valider' or text()='Validate' or text()='Confirm')])])[2]",
                    "(//button[.//span[text()='Valider']])[2]",
                    "(//button[@jsname='LgbsSe'][.//span[@jsname='V67aGc' and text()='Valider']])[2]"
                ]

                button_clicked = False
                for xpath in validate_button_xpaths:
                    try:
                        button = self.browser.find_xpath(xpath)
                        if button and button.is_displayed():
                            self.logger.info(f"Clicking validate button: {xpath}")
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(button)
                            else:
                                button.click()
                            button_clicked = True
                            break
                    except Exception as e:
                        self.logger.debug(f"Validate button xpath failed: {xpath} - {str(e)}")
                        continue

                # Try JavaScript fallback if XPath failed
                if not button_clicked:
                    try:
                        self.browser.execute_js('document.querySelectorAll("button[data-id=\'dtOep\']")[3].click()')
                        button_clicked = True
                        self.logger.info("Clicked validate button using JavaScript")
                    except Exception as e:
                        self.logger.debug(f"JavaScript validate button click failed: {str(e)}")

                if not button_clicked:
                    self.logger.error("Could not click validate button")
                    return False

                sleep(uniform(2.0, 4.0))

                # Mark account as having 2FA enabled in GmailAccountsMap.json
                self._update_2fa_status(self.browser.email, status=True)

                # Update profile manager if available
                try:
                    if hasattr(self.browser, 'profile_manager') and self.browser.profile_manager:
                        profile = self.browser.profile_manager.get_profile(self.browser.email)
                        if profile:
                            profile['2fa_enabled'] = True
                            profile['2fa_setup_date'] = datetime.now().isoformat()
                            self.browser.profile_manager._save_profiles_config()
                            self.logger.info("Updated profile manager with 2FA status")
                except Exception as e:
                    self.logger.warning(f"Could not update profile manager: {str(e)}")

                self.logger.info("2FA setup completed successfully!")
                return True

            except Exception as e:
                self.logger.error(f"Error during OTP verification: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"Error during 2FA setup: {str(e)}")
            return False

    def _update_2fa_status(self, email, status=True):
        """
        Update the 2FA enabled status in the Gmail accounts map.

        Args:
            email (str): Account email
            status (bool): 2FA enabled status
        """
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            # Find and update the account
            for item in data:
                if item['email'] == email:
                    item['2fa_enabled'] = status
                    item['2fa_setup_date'] = datetime.now().isoformat()
                    break
            else:
                # Account not found, add it
                new_account = {
                    'email': email,
                    'password': '',  # Will be updated elsewhere
                    'ua': '',
                    'email_conf': '',
                    'phone': '',
                    'status': 'active',
                    '2fa_enabled': status,
                    '2fa_setup_date': datetime.now().isoformat()
                }
                data.append(new_account)

            with open(gmail_map_file, 'w') as f:
                json.dump(data, f, indent=4)

            self.logger.info(f"Updated 2FA status in Gmail map for {email}: {status}")

        except Exception as e:
            self.logger.error(f"Error updating 2FA status: {str(e)}")

    def _should_perform_sms_verification(self):
        """
        Check if we should perform SMS verification for this account.
        Returns True if SMS verification hasn't been completed successfully yet.
        """
        try:
            # Check if sms_verification_completed flag exists in Gmail accounts map
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            for item in data:
                if item['email'] == self.browser.email:
                    # If sms_verification_completed field doesn't exist or is False, we should verify
                    sms_verified = item.get('sms_verification_completed', False)
                    self.logger.info(f"SMS verification status for {self.browser.email}: {sms_verified}")
                    return not sms_verified

            # If account not found in map, assume verification needed
            self.logger.info(f"Account {self.browser.email} not found in map, SMS verification may be needed")
            return True

        except Exception as e:
            self.logger.error(f"Error checking SMS verification status: {str(e)}")
            # Default to performing verification if we can't determine status
            return True

    def _update_sms_verification_status(self, email, status=True, phone_number=None, verification_method='5sim'):
        """
        Update the SMS verification status in the Gmail accounts map.

        Args:
            email (str): Email address
            status (bool): SMS verification completed status (default: True)
            phone_number (str): Phone number used for verification (optional)
            verification_method (str): Method used for verification (default: '5sim')
        """
        try:
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)

            # Find and update the account
            for item in data:
                if item['email'] == email:
                    item['sms_verification_completed'] = status
                    if phone_number:
                        item['last_verification_phone'] = phone_number
                    item['verification_method'] = verification_method
                    item['sms_verification_date'] = datetime.now().isoformat()
                    break
            else:
                # Account not found, add it
                new_account = {
                    'email': email,
                    'password': '',  # Will be updated elsewhere
                    'ua': '',
                    'email_conf': '',
                    'phone': '',
                    'status': 'active',
                    'sms_verification_completed': status,
                    'verification_method': verification_method,
                    'sms_verification_date': datetime.now().isoformat()
                }
                if phone_number:
                    new_account['last_verification_phone'] = phone_number
                data.append(new_account)

            # Save updated data
            with open(gmail_map_file, 'w') as f:
                json.dump(data, f, indent=4)

            self.logger.info(f"Updated SMS verification status for {email}: {status}")

            # Also update profile configuration if enhanced driver is available
            try:
                if hasattr(self, 'browser') and hasattr(self.browser, 'profile_manager'):
                    profile_id = self.browser.profile_manager._generate_profile_id(email)
                    if profile_id in self.browser.profile_manager.profiles_config:
                        profile = self.browser.profile_manager.profiles_config[profile_id]
                        if 'account_settings' not in profile:
                            profile['account_settings'] = {}
                        profile['account_settings']['sms_verification_completed'] = status
                        profile['account_settings']['sms_verification_date'] = datetime.now().isoformat()
                        profile['account_settings']['verification_method'] = verification_method
                        if phone_number:
                            profile['account_settings']['last_verification_phone'] = phone_number
                        self.browser.profile_manager._save_profiles_config()
                        self.logger.info(f"Updated profile configuration for SMS verification: {email}")
            except Exception as profile_error:
                self.logger.debug(f"Could not update profile configuration: {str(profile_error)}")

        except Exception as e:
            self.logger.error(f"Error updating SMS verification status: {str(e)}")


    def _change_gmail_language_to_french(self):
        """
        Change Google account language to French using the direct Google Account language settings page.
        This is more reliable than trying to change it through Gmail settings.
        """
        try:
            self.logger.info("Starting Google account language change to French...")

            # Save current URL to return to later
            original_url = self.browser.this_url()

            # Navigate to Google Account language settings page
            language_settings_url = "https://myaccount.google.com/language"
            self.browser.go(language_settings_url)
            sleep(uniform(3.0, 5.0))

            # Wait for language settings page to load with better detection
            page_loaded = False
            try:
                # Try multiple indicators that the page has loaded
                wait_selectors = [
                    '//button[contains(@aria-label, "Edit") or contains(@aria-label, "تعديل") or contains(@aria-label, "Modifier")]'
                ]

                for selector in wait_selectors:
                    try:
                        self.browser.wait_xpath_presence(selector, timeout=1)
                        self.logger.info(f"Language settings page loaded - detected via: {selector}")
                        page_loaded = True
                        break
                    except:
                        continue

                if not page_loaded:
                    self.logger.warning("Language settings page may not have loaded completely, continuing...")
            except Exception as e:
                self.logger.warning(f"Error waiting for language settings page: {str(e)}")

            # Step 1: Click the edit language button
            edit_button_found = self._click_edit_language_button()

            if edit_button_found:
                # Step 2: Enter "Français" in the language input field
                language_input_success = self._enter_french_language()

                if language_input_success:
                    # Step 3: Select French from the dropdown suggestions
                    french_selected = self._select_french_from_suggestions()

                    if french_selected:
                        # Step 4: Select a French-speaking country (excluding African countries)
                        country_selected = self._select_french_country()

                        if country_selected:
                            self.logger.info("Selected French country successfully")


                            self._save_google_account_language_settings()

                            sleep(uniform(0.5, 1.0))

                            # Try to remove second language
                            remove_xpaths = [
                                "//button[contains(@aria-label, 'Supprimer') or contains(@aria-label, 'Delete') or contains(@aria-label, 'Remove')]",
                                "//button[contains(@aria-label, 'Supprimer') and contains(@aria-label, 'langue')]",
                                "(//button[@jsname='Pr7Yme' and contains(@class, 'pYTkkf-Bz112c-LgbsSe')])[4]"
                            ]

                            for xpath in remove_xpaths:
                                try:
                                    button = self.browser.find_xpath(xpath)
                                    if button and button.is_displayed():
                                        if hasattr(self.browser, 'human_click_element'):
                                            self.browser.human_click_element(button)
                                        else:
                                            button.click()

                                        sleep(uniform(0.5, 1.0))

                                        # Try to confirm deletion
                                        confirm_xpaths = [
                                            "//button[.//span[text()='Supprimer'] or .//span[text()='Delete'] or .//span[text()='Remove']]",
                                            "//button[@data-mdc-dialog-action='ok' and contains(@class, 'mUIrbf-LgbsSe')]"
                                        ]

                                        for confirm_xpath in confirm_xpaths:
                                            try:
                                                confirm_btn = self.browser.find_xpath(confirm_xpath)
                                                if confirm_btn and confirm_btn.is_displayed():
                                                    if hasattr(self.browser, 'human_click_element'):
                                                        self.browser.human_click_element(confirm_btn)
                                                    else:
                                                        confirm_btn.click()
                                                    break
                                            except:
                                                continue

                                        self.logger.info("Removed second language successfully")
                                        break
                                except:
                                    continue

                            # Update the language_changed status
                            self._update_language_changed_status(self.browser.email, True)

                            self.logger.info("Google account language change to French completed successfully")
                        else:
                            self.logger.warning("Could not select French country, but proceeding to save")
                            self._save_google_account_language_settings()
                            self._update_language_changed_status(self.browser.email, True)

                    else:
                        self.logger.warning("Could not select French from suggestions")
                        self._update_language_changed_status(self.browser.email, True)  # Mark as attempted

                else:
                    self.logger.warning("Could not enter French in language input")
                    self._update_language_changed_status(self.browser.email, True)  # Mark as attempted

            else:
                self.logger.warning("Could not find edit language button")
                self._update_language_changed_status(self.browser.email, True)  # Mark as attempted


            # Wait a moment for settings to save
            sleep(uniform(2.0, 3.0))

            # Return to original URL or Gmail inbox
            if original_url and ('gmail.com' in original_url or 'google.com' in original_url):
                self.browser.go(original_url)
            else:
                self.browser.go("https://mail.google.com/mail/u/0/#inbox")

            sleep(uniform(2.0, 3.0))

        except Exception as e:
            self.logger.error(f"Error changing Google account language to French: {str(e)}")
            # Even if language change fails, mark as attempted to avoid repeated attempts
            self._update_language_changed_status(self.browser.email, True)


 

    def _click_edit_language_button(self):
        """
        Click the edit language button on Google Account language settings page.
        Handles both English and Arabic interfaces.
        """
        try:
            # Button selectors for different languages and states
            edit_button_selectors = [
                '//button[contains(@aria-label, "Edit") or contains(@aria-label, "تعديل") or contains(@aria-label, "Modifier")]'
            ]

            # Try to find edit button with shorter timeout per selector
            for selector in edit_button_selectors:
                try:
                    # Use a shorter timeout for each selector to avoid long waits
                    button = self.browser.wait_xpath_presence(selector, timeout=1)
                    if button and button.is_displayed():
                        self.logger.info(f"Found edit language button: {selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(button)
                        else:
                            button.click()

                        sleep(uniform(0.5, 1.0))
                        self.logger.info("Clicked edit language button successfully")
                        return True

                except Exception as e:
                    self.logger.debug(f"Edit button selector {selector} failed: {str(e)}")
                    continue

            # If no button found, check if we're already in edit mode
            try:
                input_field = self.browser.find_xpath('//input[@role="combobox"]')
                if input_field and input_field.is_displayed():
                    self.logger.info("Language input field already visible - edit mode already active")
                    return True
            except:
                pass

            self.logger.warning("Could not find edit language button")
            return False

        except Exception as e:
            self.logger.error(f"Error clicking edit language button: {str(e)}")
            return False



    def _enter_french_language(self):
        """
        Enter "Français" in the language input field.
        """
        try:
            # Input field selectors
            input_selectors = [
                '//input[contains(@aria-label, "language") or contains(@aria-label, "لغة") or contains(@aria-label, "langue")]',
                '//input[@class="qdOxv-fmcmS-wGMbrd"]',
            ]

            for selector in input_selectors:
                try:
                    input_field = self.browser.find_xpath(selector)
                    if input_field and input_field.is_displayed():
                        self.logger.info(f"Found language input field: {selector}")
                        input_field.clear()
                        sleep(uniform(0.5, 0.8))

                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(input_field, "Français")
                        else:
                            input_field.send_keys("Français")

                        sleep(uniform(0.5, 0.8))
                        self.logger.info("Entered 'Français' in language input field")
                        return True

                except Exception as e:
                    self.logger.debug(f"Input selector {selector} failed: {str(e)}")
                    continue

            self.logger.warning("Could not find language input field")
            return False

        except Exception as e:
            self.logger.error(f"Error entering French language: {str(e)}")
            return False



    def _select_french_from_suggestions(self):
        """
        Select French from the dropdown suggestions that appear after typing.
        """
        try:
            # Wait for suggestions to appear
            sleep(uniform(1.0, 2.0))

            # Suggestion selectors for French
            french_suggestion_selectors = [
                '//span[contains(text(), "Français")]',
            ]

            for selector in french_suggestion_selectors:
                try:
                    suggestion = self.browser.find_xpath(selector)
                    if suggestion and suggestion.is_displayed():
                        self.logger.info(f"Found French suggestion: {selector}")

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(suggestion)
                        else:
                            suggestion.click()

                        sleep(uniform(0.5, 0.8))
                        self.logger.info("Selected French from suggestions")
                        return True

                except Exception as e:
                    self.logger.debug(f"French suggestion selector {selector} failed: {str(e)}")
                    continue


            self.logger.warning("Could not find French language suggestion")
            return False

        except Exception as e:
            self.logger.error(f"Error selecting French from suggestions: {str(e)}")
            return False



    def _select_french_country(self):
        """Select first or second country option from dropdown"""
        try:
            self.logger.info("Selecting country option...")
            sleep(uniform(0.5, 1.0))

            # Try to find country options
            selectors = ['//li[@role="option"]', '//*[@role="listbox"]//li']

            for selector in selectors:
                try:
                    if hasattr(self.browser, 'find_elements'):
                        elements = self.browser.find_elements(By.XPATH, selector)
                    else:
                        elements = self.browser.find_xpath_all(selector) if hasattr(self.browser, 'find_xpath_all') else []

                    # Filter visible elements
                    visible_elements = [el for el in elements if el.is_displayed() and el.text.strip()]

                    if visible_elements:
                        # Click first or second option (randomly choose)
                        chosen_index = 0 if len(visible_elements) == 1 else choice([0, 5])
                        chosen_element = visible_elements[chosen_index]

                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(chosen_element)
                        else:
                            chosen_element.click()

                        sleep(uniform(0.5, 1.0))
                        self.logger.info(f"Selected country option: {chosen_element.text.strip()}")
                        return True
                except:
                    continue

            self.logger.warning("No country options found")
            return False

        except Exception as e:
            self.logger.error(f"Error selecting country: {str(e)}")
            return False




    def _save_google_account_language_settings(self):
        """
        Save the Google Account language settings after selecting French.
        """
        selector = '//button[.//span[text()="Save"] or .//span[text()="حفظ"] or .//span[text()="Enregistrer"]]'
        try:
            click_success = False
            save_button = self.browser.find_xpath(selector)
            if save_button and save_button.is_displayed():
                if hasattr(self.browser, 'human_click_element'):
                    try:
                        self.browser.human_click_element(save_button)
                        click_success = True
                        self.logger.info("Clicked save button using human_click_element")
                    except Exception as e:
                        self.logger.debug(f"Human click failed: {str(e)}")


                elif not click_success:
                    try:
                        save_button.click()
                    except:
                        return False


                if click_success:
                    sleep(uniform(0.5, 1.0))
                    self.logger.info("Successfully clicked save button for language settings")
                    self.logger.info("Language change completed")              
                    return True
                else:
                    self.logger.warning(f"All click methods failed for save button: {selector}")
                    return False
        except:
            return False
                    



 

    def _detect_account_recovery(self):
        """Detect account recovery challenges"""
        try:
            recovery_indicators = [
                "account recovery",
                "récupération de compte",
                "when did you create this account",
                "quand avez-vous créé ce compte",
                "last password you remember",
                "dernier mot de passe dont vous vous souvenez"
            ]

            for indicator in recovery_indicators:
                if self.check_js(f'"{indicator}"'):
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting account recovery: {str(e)}")
            return False



    def _handle_account_recovery(self):
        """Handle account recovery challenges"""
        self.logger.warning("### ACCOUNT RECOVERY REQUIRED ###")
        self.logger.warning("Google is requesting account recovery information")
        self.update_email_status(self.browser.email, "recovery_required")

        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self.logger.warning("Please complete account recovery manually")

        # Wait for manual completion
        self._wait_for_verification_completion()
        return True

    def _detect_terms_update(self):
        """Detect terms of service updates"""
        try:
            terms_indicators = [
                "Terms of Service",
                "Privacy Policy",
                "Conditions d'utilisation",
                "Règles de confidentialité",
                "I agree",
                "J'accepte",
                "Accept",
                "Accepter"
            ]

            for indicator in terms_indicators:
                if self.check_js(f'"{indicator}"'):
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting terms update: {str(e)}")
            return False

    def _handle_terms_update(self):
        """Handle terms of service updates"""
        self.logger.info("### TERMS OF SERVICE UPDATE ###")
        self.logger.info("Google is requesting acceptance of updated terms")

        # Try to accept terms
        accept_buttons = [
            '//button[contains(text(), "I agree")]',
            '//button[contains(text(), "Accept")]',
            '//button[contains(text(), "J\'accepte")]',
            '//button[contains(text(), "Accepter")]',
            '//button[@type="submit"]'
        ]

        for button_xpath in accept_buttons:
            try:
                button = self.browser.find_xpath(button_xpath)
                if button:
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(button)
                    else:
                        button.click()

                    sleep(uniform(2.0, 4.0))
                    self.logger.info("Terms accepted successfully")
                    return False  # Continue with human actions
            except:
                continue

        # If no accept button found, wait for manual intervention
        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self.logger.warning("Please accept terms of service manually")
        self._wait_for_verification_completion()
        return True

    def _handle_security_question(self):
        """Handle security questions"""
        self.logger.warning("### SECURITY QUESTION DETECTED ###")
        self.logger.warning("Google is asking a security question")
        self.update_email_status(self.browser.email, "security_question")

        self.logger.warning("### MANUAL INTERVENTION REQUIRED ###")
        self.logger.warning("Please answer the security question manually")

        # Wait for manual completion
        self._wait_for_verification_completion()
        return True



    def fix_errors(self):
        print("Fixing Errors!!")
        
        while self.browser.running() == True:
            sleep(2.5)



    def check_groups(self):
        self.browser.go("https://groups.google.com/my-groups?hl=fr-FR")
        self.browser.wait_xpath_presence('//span[contains(., "Créer un groupe")]')
        try:
            groups_list = self.browser.find_xpath_all('//div[@data-rowid]/div')
            if groups_list :
                try:
                    self.add_group(self.browser.email, self.get_group_name())
                except:
                    pass
                return True
            else:
                return False
        except:
            return False

    

    def get_group_name(self):
        #self.login_Wait()
        try:
            grp_name = self.browser.execute_js('return document.querySelectorAll("#yDmH0d > c-wiz.zQTmif.SSPGKf.eejsDc > c-wiz > div > div.U3yzR > div > div.ReSblb.OcVpRe.eFM3be > div:nth-child(2) > div:nth-child(1) > div")[0].attributes[2].value')
        except Exception as e:
            self.logger.error(f"JavaScript execution failed: {e}")
            try:
                grp_name = self.browser.find_xpath_all('//div[@data-rowid]/div')[0].get_attribute("data-group-name")
            except Exception as e:
                self.logger.error(f"XPath execution failed: {e}")
                grp_name = None

        return grp_name


    def get_members_num(self):
        try:
            grp_num = self.browser.execute_js('document.querySelectorAll("#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > div")[0].textContent')
            grp_num = grp_num.replace(" membres","").replace(" membre","")
        except:
            try:
                grp_num = self.browser.find_css('#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > div').text
                grp_num = grp_num.replace(" membres","").replace(" membre","")
            except:
                grp_num = 0
        return grp_num




    def get_members_js(self):
        dig = ''.join(choice(digits) for _ in range(5))
        grp_num = f"grpnum{dig}"
        func = """
        function getnum() {
        mbr = []
        Node_list = document.getElementsByTagName('div')
        for (let i = 0; i < Node_list.length; i++) {
        if (Node_list[i].innerHTML.indexOf("&nbsp;membre") !== -1){
            mbr.push(Node_list[i])
        }
        }
        return  mbr.at(-1).textContent;
        }
        getnum()
        var newDiv = document.createElement("div");
        var newContent = document.createTextNode(getnum());
        newDiv.appendChild(newContent);
        var currentDiv = document.getElementById('div1');
        document.body.insertBefore(newDiv, currentDiv);
        newDiv.setAttribute('id','%s')
        """ %(grp_num)
        self.browser.execute_js(func)
        num = self.browser.find_css(f"#{grp_num}").text
        return num.replace(" membres","").replace(" membre","")



    def accpet_invite(self):
        try:
            self.browser.find_xpath('//div[@aria-label="Ajouter les membres directement. Si vous désactivez cette option, les membres recevront une invitation."]').click()
        except:
            try:
                self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[1]/div/div[5]/div[1]').click()
            except:
                try:
                    self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.fNxzgd.VhQQpd.Niudaf.Inn9w.iWO5td > span > c-wiz > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.qs41qe > span > div:nth-child(2) > div > div.UY6sJb > div.LsSwGf.SWVgue.br5iZc').click()
                except:
                    self.logger.error(f"Can't Find Send Invitations Button!! [Accept_invite]]")



    def pass_members(self):
        captcha_failed = False
        try:
            self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[2]/div[2]/span/span').click()
        except:
            try:
                self.browser.execute_js(
                """Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[21].click(); """)
            except:
                self.logger.error(f"Can't Find Send Invitations Button!! [pass_members[1]]")
        
        
        sleep(uniform(1.4,2.2))


        try:
            self.CaptchaSolver()
        except Exception as e:
            self.logger.error(f"+++ Captcha Solver {str(e)} +++")
            captcha_failed = True
            #self.terminate_selenium_driver()
            #return
        
        if captcha_failed == False:
            sleep(uniform(0.5,1.5))
            
            try:
                self.browser.execute_js(
                    """ Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[23].click(); """)
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[2]/span/div/div[2]/div[1]/span/span').click()
                except:
                    try:
                        self.browser.execute_js("Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[23].click();")
                    except:
                        self.logger.error(f"Can't Find Send Invitations Button!! [pass_members[2]]")
                        #self.browser.finish()
                        #self.terminate_selenium_driver()
                        #return
                



    def get_emails(self):
        files = [f for f in os.listdir(data_directory) if os.path.isfile(os.path.join(data_directory, f))]

        files.sort(key=lambda f: int(f.split('_')[1].split('.')[0]))

        with open(os.path.join(data_directory, files[0]), 'r') as file:
            lines = [line.strip() for line in file.readlines()]

        return lines, files[0]
    


    def update_group_list(self, grp_name):
        # Placeholder method - currently not implemented
        _ = grp_name  # Suppress unused parameter warning
        return
    


    def get_group_admins(self, email, group):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"] == group:
                    admins = g.get("admins", "No admins found")
                    return admins.split(",") if admins else []

        return None


    def recovery_file(self,file_name,emails):
        with open(os.path.join(data_directory, file_name), 'w') as file:
            for email in emails:
                file.write(f"{email}\n")


    def delete_file(self,file_path):
        if os.path.exists(os.path.join(data_directory,file_path)):
            os.remove(os.path.join(data_directory,file_path))
            self.logger.info(f"### File {file_path} deleted successfully ###")
        else:
            self.logger.error("+++ The file does not exist +++")


    def sync_new_accounts_from_file(self):
        """
        Sync new accounts from Gmail_Accounts file to GmailAccountsMap.json
        This function checks for accounts in Gmail_Accounts that are not in the map and adds them
        """
        try:
            # Load existing account map
            existing_accounts = {}
            if os.path.exists(gmail_map_file):
                with open(gmail_map_file, 'r') as json_file:
                    try:
                        existing_data = json.load(json_file)
                        if existing_data:
                            for account in existing_data:
                                if isinstance(account, dict) and 'email' in account:
                                    existing_accounts[account['email']] = account
                    except json.JSONDecodeError:
                        self.logger.warning("Invalid JSON in gmail_map_file. Will recreate.")
                        existing_data = []
            else:
                existing_data = []

            # Load user agent list
            ua_list = []
            if os.path.exists(ua_map):
                with open(ua_map, 'r') as ua_file:
                    ua_list = json.load(ua_file)

            if not ua_list:
                # Fallback user agent if ua_map doesn't exist
                ua_list = ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]

            # Read Gmail_Accounts file and check for new accounts
            new_accounts_added = 0
            if os.path.exists(gmail_account_file):
                with open(gmail_account_file, 'r') as file:
                    for line_num, line in enumerate(file, 1):
                        line = line.strip()
                        if not line or line.startswith('#'):  # Skip empty lines and comments
                            continue

                        try:
                            parts = line.split(':')
                            if len(parts) < 2:
                                parts = line.split(';')

                            if len(parts) >= 2:
                                email = parts[0].strip()
                                password = parts[1].strip()
                                conf = parts[2].strip() if len(parts) >= 3 else ""

                                if not email or not password:
                                    self.logger.warning(f"Line {line_num}: Invalid email or password format: {line}")
                                    continue

                                # Check if account already exists in map
                                if email not in existing_accounts:
                                    # New account found - add it to the map
                                    new_account = {
                                        'email': email,
                                        'password': password,
                                        'ua': choice(ua_list),
                                        "email_conf": conf if "@" in conf else "",
                                        "phone": conf if "@" not in conf else "",
                                        'status': "null"
                                    }
                                    existing_accounts[email] = new_account
                                    new_accounts_added += 1
                                    self.logger.info(f"NEW ACCOUNT DETECTED: Added {email} to account map")
                                else:
                                    # Account exists - update password if it has changed
                                    if existing_accounts[email]['password'] != password:
                                        existing_accounts[email]['password'] = password
                                        self.logger.info(f"UPDATED PASSWORD: Updated password for {email}")
                            else:
                                self.logger.warning(f"Line {line_num}: Invalid format (expected email:password:recovery): {line}")

                        except Exception as e:
                            self.logger.error(f"Line {line_num}: Error parsing account data: {str(e)} - Line: {line}")

            # Save updated account map
            updated_accounts_list = list(existing_accounts.values())
            with open(gmail_map_file, 'w') as json_file:
                json.dump(updated_accounts_list, json_file, indent=4)

            if new_accounts_added > 0:
                self.logger.info(f"ACCOUNT SYNC COMPLETE: Added {new_accounts_added} new account(s) to the map")
                self.logger.info(f"Total accounts in map: {len(updated_accounts_list)}")
            else:
                self.logger.info("ACCOUNT SYNC COMPLETE: No new accounts found")

        except Exception as e:
            self.logger.error(f"Error syncing new accounts: {str(e)}")

    def force_sync_accounts(self):
        """
        Force sync all accounts from Gmail_Accounts file to GmailAccountsMap.json
        This can be called manually to ensure all accounts are synchronized
        """
        self.logger.info("FORCE SYNC: Synchronizing all accounts from Gmail_Accounts file...")
        self.sync_new_accounts_from_file()

    def create_accounts_map(self):
        gmail_accounts_map = []
        if os.path.exists(gmail_map_file):
            with open(gmail_map_file, 'r') as json_file:
                try:
                    existing_data = json.load(json_file)
                    if existing_data and len(existing_data) > 0:
                        # Validate the structure of existing data
                        first_account = existing_data[0]
                        if isinstance(first_account, dict) and 'email' in first_account:
                            self.logger.info("Accounts already exist in gmail_map_file. Checking for new accounts...")
                            # Always sync new accounts even if map exists
                            self.sync_new_accounts_from_file()
                            return
                        else:
                            self.logger.warning("Invalid account structure detected. Regenerating accounts map.")
                except json.JSONDecodeError:
                    self.logger.warning("Invalid JSON in gmail_map_file. Regenerating accounts map.")
                    pass


        with open(ua_map, 'r') as ua_file:
            ua_list = json.load(ua_file)
        
        with open(gmail_account_file, 'r') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line or line.startswith('#'):  # Skip empty lines and comments
                    continue

                try:
                    parts = line.split(':')
                    if len(parts) < 2:
                        parts = line.split(';')

                    if len(parts) >= 2:
                        email = parts[0].strip()
                        password = parts[1].strip()
                        conf = parts[2].strip() if len(parts) >= 3 else ""

                        if not email or not password:
                            self.logger.warning(f"Line {line_num}: Invalid email or password format: {line}")
                            continue

                        gmail_account = {
                            'email': email,
                            'password': password,
                            'ua': choice(ua_list),
                            "email_conf": conf if "@" in conf else "",
                            "phone": conf if "@" not in conf else "",
                            'status': "null"
                        }
                        gmail_accounts_map.append(gmail_account)
                        self.logger.info(f"Added account: {email}")
                    else:
                        self.logger.warning(f"Line {line_num}: Invalid format (expected email:password:recovery): {line}")

                except Exception as e:
                    self.logger.error(f"Line {line_num}: Error parsing account data: {str(e)} - Line: {line}")

        with open(gmail_map_file, 'w') as json_file:
            json.dump(gmail_accounts_map, json_file, indent=4)


    def create_data_parts(self,cmd=None):
        if not os.path.exists(data_directory):
            os.makedirs(data_directory)

        existing_files = [
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f)) and re.match(r'data_(\d+)\.txt$', f)
        ]

        if cmd is not None:
            if existing_files:
                existing_numbers = [
                    int(re.findall(r'data_(\d+)\.txt', f)[0]) for f in existing_files
                ]
                max_number = max(existing_numbers)
            else:
                max_number = 0

            with open(data_file, 'r') as file:
                lines = file.readlines()

            start_line = max_number * 90
            total_lines = len(lines)

            if start_line < total_lines:
                for i in range(start_line, total_lines, 90):
                    part_number = (i // 90) + 1
                    part_file_path = os.path.join(data_directory, f"data_{part_number}.txt")
                    with open(part_file_path, 'w') as part_file:
                        part_file.writelines(lines[i:i+90])
                self.logger.info(f"Generated data files from {max_number + 1} to {part_number}")
            else:
                self.logger.info("All data files are up to date.")
        else:
            if not existing_files:
                with open(data_file, 'r') as file:
                    lines = file.readlines()
                    for i in range(0, len(lines), 90):
                        part_file_path = os.path.join(data_directory, f"data_{i//90 + 1}.txt")
                        with open(part_file_path, 'w') as part_file:
                            part_file.writelines(lines[i:i+90])
            else:
                self.logger.info("### Data parts already exist. Skipping generation. ###")
                total_files = len(existing_files)
                self.logger.info(f"### Data Files N: {total_files * 90} ###")
            



    def create_groups(self):
        failed = False
        self.grp_name = CityName().name()

        # Try to find and click "Create Group" button with humanized clicking
        try:
            create_button = self.browser.find_xpath('//*[@id="yDmH0d"]/c-wiz[1]/div/div/gm-coplanar-drawer/div/div/span/div/div/div/div[1]/div/button')
            if hasattr(self.browser, 'human_click_element'):
                self.browser.human_click_element(create_button)
            else:
                create_button.click()
        except:
            try:
                self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[0].click();
                                """)
            except:
                try:
                    create_button = self.browser.find_xpath("//button/span[text()='Créer un groupe']")
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(create_button)
                    else:
                        create_button.click()
                except:
                    self.logger.error("Can't Find Create Group Button!!")
                    failed = True

        sleep(uniform(0.5,0.8))

        if not failed:
            sleep(uniform(0.5,0.8))
            #Select regular Groups
            #* Group Name - Use humanized typing
            try:
                group_name_input = self.browser.find_xpath("//input[@aria-label='Nom du groupe']")
                if hasattr(self.browser, 'human_type_text'):
                    self.browser.human_type_text(group_name_input, self.grp_name)
                else:
                    group_name_input.send_keys(self.grp_name)
            except:
                try:
                    self.browser.execute_js(f""" document.querySelector('[aria-label="Nom du groupe"]').value = "{self.grp_name}")""" )
                    self.browser.execute_js(f""" document.querySelector('[aria-label="Préfixe d\'adresse e-mail du groupe"]').value = "{self.grp_name.lower()}" """)
                except:
                    try:
                        group_name_input = self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[1]/span/div/div[2]/div[2]/div/div/div/div[1]/div[1]/input')
                        if hasattr(self.browser, 'human_type_text'):
                            self.browser.human_type_text(group_name_input, self.grp_name)
                        else:
                            group_name_input.send_keys(self.grp_name)
                    except:
                        pass

            sleep(uniform(0.8,1.2))

            # Click "Next" button with humanized clicking
            try:
                next_button = self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(1) > span > span")
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(next_button)
                else:
                    next_button.click()
            except:
                try:
                    self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Suivant' })[3].click();
                                    """)
                except:
                    try:
                        next_button = self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[1]/span/span')
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(next_button)
                        else:
                            next_button.click()
                    except:
                        pass
            sleep(1.3)

            #Publier Les Messages: - Use humanized clicking
            try:
                publish_button = self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > span > c-wiz > div > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.NcKcee.qs41qe > span > div > div.kzQLre > div:nth-child(4) > div.J6Z1mf > div.yEhNJc > div.xk0jZ.AjVdrc.x9Ufpf")
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(publish_button)
                else:
                    publish_button.click()
            except:
                try:
                    publish_button = self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[2]/span/div/div[2]/div[4]/div[2]/div[1]/div[2]')
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(publish_button)
                    else:
                        publish_button.click()
                except:
                    pass
            sleep(0.5)

            # Afficher Liste Membres: - Use humanized clicking
            try:
                members_button = self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > span > c-wiz > div > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.NcKcee.qs41qe > span > div > div.kzQLre > div:nth-child(5) > div.J6Z1mf > div.yEhNJc > div.xk0jZ.AjVdrc.x9Ufpf.qnnXGd")
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(members_button)
                else:
                    members_button.click()
            except:
                try:
                    members_button = self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[2]/span/div/div[2]/div[5]/div[2]/div[1]/div[2]')
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(members_button)
                    else:
                        members_button.click()
                except:
                    pass

            sleep(0.5)

            # Click next button with humanized clicking
            try:
                next_button = self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(4) > span > span")
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(next_button)
                else:
                    next_button.click()
            except:
                try:
                    self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Suivant' })[3].click();
                                    """)
                except:
                    try:
                        next_button = self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[1]/span/span')
                        if hasattr(self.browser, 'human_click_element'):
                            self.browser.human_click_element(next_button)
                        else:
                            next_button.click()
                    except:
                        pass

            
            sleep(0.7)

            """
            if admins is not None:

                self.accpet_invite()

                sleep(0.7)

                list_admins = admins.split(",")
                for admin in list_admins:
                    sleep(uniform(0.1,0.3))
                    try:
                        self.browser.find_xpath("//input[@aria-label='Gestionnaires du groupe']").send_keys(admin.strip())
                    except:
                        self.browser.find_xpath("(//input[@spellcheck='false'])[2]").send_keys(admin.strip())
                    sleep(uniform(0.1,0.2))
                    try:
                        self.browser.find_xpath("//input[@aria-label='Gestionnaires du groupe']").send_keys(Keys.RETURN)
                    except:
                        self.browser.find_xpath("(//input[@spellcheck='false'])[2]").send_keys(Keys.RETURN)

                sleep(0.5)
            """

            # Final "Create Group" button with humanized clicking
            try:
                create_group_button = self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(6) > span > span')
                if hasattr(self.browser, 'human_click_element'):
                    self.browser.human_click_element(create_group_button)
                else:
                    create_group_button.click()
            except:
                try:
                    create_group_button = self.browser.find_xpath("//div[@role='button'][./span/span[text()='Créer un groupe']])[3]")
                    if hasattr(self.browser, 'human_click_element'):
                        self.browser.human_click_element(create_group_button)
                    else:
                        create_group_button.click()
                except:
                    try:
                        self.browser.execute_js("""
                        Array.prototype.slice.call(document.querySelectorAll('div')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[4].click()
                                        """)
                    except:
                        try:
                            create_group_button = self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[6]/span/span')
                            if hasattr(self.browser, 'human_click_element'):
                                self.browser.human_click_element(create_group_button)
                            else:
                                create_group_button.click()
                        except:
                            self.browser.finish()
                            self.terminate_selenium_driver()
                            self.logger.error(f"Can't Find Create Group Button!!")
                            return


            sleep(2)
            try:
                self.CaptchaSolver(self.browser.this_url())
            except Exception as e:
                self.logger.error(f"{str(e)}")
                #self.terminate_selenium_driver()


            sleep(1.5)

            try:
                self.browser.execute_js("""
                Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[3].click();""")
            except:
                try:
                    self.browser.find_xpath("//div[@aria-label='Créer un groupe'][./span/span[text()='Créer un groupe']]").click()
                except:
                    self.logger.error(f"Can't Find Create Group Button After Captcha!!")
                    #self.browser.finish()
                    #self.terminate_selenium_driver()

            sleep(1.5)

            retry_count = 0
            max_retries = 3

            while retry_count < max_retries:
                sleep(uniform(5.5, 7.5))
                if self.check_groups():
                    self.add_group(self.browser.email, self.grp_name.lower())
                    self.logger.info(f"Group {self.grp_name} Created Successfully!!")
                    break
                self.logger.info(f"Retry {retry_count + 1}: Group Creation Failed!!")
                retry_count += 1

                if retry_count == max_retries:
                    self.logger.error(f"Failed to create group after {max_retries} retries.")
                    with open("DeadAccounts.txt", "a") as file:
                        file.write(f"{self.browser.email}\n")
        



    def upload(self):
        self.grp_name = self.get_groups(self.browser.email)[0]
        self.grp_url =  f"https://groups.google.com/g/{self.grp_name}"
        self.browser.go(self.grp_url + "/members?hl=fr-FR")
        self.uploaded_data = []
        #self.i = 0
        error_404 = self.check_js("L'URL demandée est introuvable sur ce serveur. C'est tout")
        error_404 = False
        if error_404 is True:
            self.remove_group(self.browser.email,self.grp_name)
        else:
            try:
                self.first_members_num = self.get_members_js()
                self.update_group_members(self.browser.email,self.grp_name.lower(),self.first_members_num)
            except:
                try:
                    self.first_members_num = self.get_members_num()
                    self.update_group_members(self.browser.email,self.grp_name.lower(),self.first_members_num)
                except:
                    self.first_members_num = 0
            self.logger.info(f"### Group Members: {self.first_members_num} ###")

            try:
                self.emails, file_name = self.get_emails()
            except Exception as e:
                self.logger.error(f"{str(e)}")
                self.browser.finish()
                self.terminate_selenium_driver()
                self.logger.error("+++ Can't get data!! +++")
                return
            
            try:
                self.browser.wait_xpath_presence('//div[@aria-label="Ajouter"]/span/span[contains(., "Ajouter")]')
            except:
                self.browser.wait_css_clickable("#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > span > div:nth-child(1) > span > span")

            try:
                self.browser.find_xpath('//div[@aria-label="Ajouter"]/span/span[contains(., "Ajouter")]').click()
            except Exception as e:
                self.logger.error(f"{str(e)}")
                return

            sleep(uniform(0.5,1.5))


            try:
                self.browser.find_xpath('//div[@aria-label="Ajouter les membres directement. Si vous désactivez cette option, les membres recevront une invitation."]').click()
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[1]/div/div[5]/div[1]').click()
                except:
                    try:
                        self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.fNxzgd.VhQQpd.Niudaf.Inn9w.iWO5td > span > c-wiz > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.qs41qe > span > div:nth-child(2) > div > div.UY6sJb > div.LsSwGf.SWVgue.br5iZc').click()
                    except:
                        self.logger.error(f"+++ Can't Find Ajouter Directement Button!! +++")
                        
            self.logger.info(f"### Uploading  {len(self.emails)} emails to Group: {self.grp_name} ###")
            for email in self.emails:
                sleep(uniform(0.1,0.3))
                try:
                    input_element = self.browser.find_xpath("//input[@aria-label='Membres du groupe']")
                    if hasattr(self.browser, 'human_type_text'):
                        self.browser.human_type_text(input_element, email, clear_first=False)
                    else:
                        input_element.send_keys(email)
                except:
                    input_element = self.browser.find_xpath("//input[@spellcheck='false']")
                    if hasattr(self.browser, 'human_type_text'):
                        self.browser.human_type_text(input_element, email, clear_first=False)
                    else:
                        input_element.send_keys(email)
                self.uploaded_data.append(email)
                sleep(uniform(0.01,0.05))
                try:
                    self.browser.find_xpath("//input[@aria-label='Membres du groupe']").send_keys(Keys.RETURN)
                except:
                    self.browser.find_xpath("//input[@spellcheck='false']").send_keys(Keys.RETURN)
                    
            self.pass_members()

            sleep(uniform(8.5,9.5))

            quota_reached = self.check_js("Échec de l'ajout des membres, car vous avez dépassé votre limite quotidienne. Veuillez réessayer plus tard.")
            self.logger.info(f"### Quota Reached: {quota_reached} ###")

            self.uploaded_sum = len(self.uploaded_data)
            retry_count = 0
            max_retries = 3

            if quota_reached == False:
                sleep(uniform(10.5, 11.5))
                while retry_count < max_retries:
                    # Use enhanced driver's refresh method or fallback to standard refresh
                    if hasattr(self.browser, 'refresh'):
                        self.browser.refresh()
                    else:
                        self.browser.browser.refresh()  # Fallback to underlying browser
                    sleep(uniform(5.5, 7.5))
                    try:
                        self.finish_num_mem = self.get_members_js()
                    except:
                        try:
                            self.finish_num_mem = self.get_members_num()
                        except:
                            self.finish_num_mem = "0"
                    self.logger.info(f"Retry {retry_count + 1}: finish_num_mem = {self.finish_num_mem}, first_members_num = {self.first_members_num}")
                    if int(self.finish_num_mem) > int(self.first_members_num):
                        break
                    retry_count += 1
            else:
                sleep(uniform(2.5, 3.5))

            self.logger.info(f"### Group Members: {self.finish_num_mem} ###")


            if int(self.finish_num_mem) > int(self.first_members_num):
                try:
                    self.delete_file(file_name)
                    self.logger.info(f"### Data Uploaded Successfully!! ###")
                except Exception as e:
                    self.logger.error(f"{str(e)}")

            elif int(self.first_members_num) >= int(self.finish_num_mem) or quota_reached:
                self.logger.error("+++ Data Not Uploaded!! +++")
                return
            else:
                return
            self.update_group_members(self.browser.email,self.grp_name.lower(),self.finish_num_mem)
            
        sleep(2.5)



    def run(self):
        need_fix = []
        with open(gmail_map_file, 'r') as file:
            jsn_data = json.load(file)
        if len(jsn_data) == 0:
            self.logger.info("+++ No Accounts Found +++")
        
        if "fix_errors" in self.actions:
            for account in jsn_data:
                email = account["email"]
                with open(map_path, 'r') as f:
                    grp_data = json.load(f)
                    if email not in grp_data:
                        need_fix.append(email)
                    
        self.logger.info(f"### Total Accounts: {len(jsn_data)} ###")
        self.ac = 0
        for account in jsn_data:
            self.ac+=1
            print("\n")
            self.logger.info(f"====== Account : {self.ac} ======")
            self.email = account["email"]
            self.password = account["password"]
            ua_agent = account["ua"]
            status = account["status"]


            try:
                if "reload_profiles" in self.actions:
                    self.remove_profile(self.email)
                driver_instance = Driver(self.email,self.password,ua_agent,self.ac)
                self._setup_driver_references(driver_instance)
                try:
                    self.browser.go("https://accounts.google.com/signin")
                    self.logger.info(f"### Profile: {self.browser.email} ###")
                    sleep(5000)
                    if "reload_profiles" in self.actions:
                        self.browser.go("https://google.com")
                        self.logger.info(f"### Profile: {self.browser.email} ###")
                        self.logger.info(f"### Profile Reloaded: {self.browser.email} ###")
                        continue

 
                except Exception as e:
                    if "ERR_PROXY_CONNECTION_FAILED" in str(e) or "net" in str(e) or "ERR_CONNECTION_RESET" in str(e) :
                        self.logger.error(f"{str(e)}")
                        break
                sleep(1)
                current_url = self.browser.this_url()
                self.logger.info(f"DEBUG: Current URL after navigation: {current_url}")
                self.logger.info(f"DEBUG: Actions passed to script: {self.actions}")
                self.logger.info(f"DEBUG: Account status: {status}")
                

                #sleep for debug:
                #sleep(40000)

                # Check if already logged in and handle human actions for option 9
                if "https://myaccount.google.com/" in current_url:
                    self.logger.info("### Account already logged in successfully ###")
                    if "human_actions" in self.actions:
                        self.logger.info("### Performing Human Actions (Already Logged In) ###")
                        self.perform_human_actions_after_login()
                        self.update_email_status(self.browser.email, "active")
                        self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")
                        continue  # Skip to next account

                if "https://myaccount.google.com/" not in current_url or "fix_errors" in self.actions:
                    if "fix_errors" not in self.actions:
                        #if status == "active" or status == "null" or status == "phone_verification_required" or status == "suspicious_activity" or status == " email_verification_required":
                            """
                            if "?hl=fr-FR" not in self.browser.this_url():
                                self.browser.go(f"{self.browser.this_url()}?hl=fr-FR")
                            """

                            if "signinchooser" in self.browser.this_url():
                                self.signchooser()


                            if "signin/confirmidentifier" in self.browser.this_url():
                                self.webreauth()
                            

                            elif "accounts.google.com/v3/signin/identifier" in self.browser.this_url():
                                self.logger.info(f"On Google sign-in page. Actions: {self.actions}")
                                if "login" in self.actions:
                                    self.logger.info("Login action found - proceeding with login")
                                    self.login()


                                # Always perform human actions after successful login (unless explicitly disabled)
                                if "skip_human_actions" not in self.actions:
                                    self.logger.info("### Performing Human Actions After Login ###")
                                    self.perform_human_actions_after_login()

                                # Handle different action modes
                                if "human_actions" in self.actions:
                                    # For human_actions mode, we only do login + human actions, then stop
                                    self.update_email_status(self.browser.email, "active")
                                    self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")
                                    continue  # Skip to next account

                                self.update_email_status(self.browser.email, "active")

                            if "Login&Wait" in self.actions:
                                self.login_Wait()

                            elif "human_actions" not in self.actions:  # Skip group operations for human_actions mode
                                if self.check_groups() == False:
                                    self.create_groups()
                                else:
                                    self.logger.info(f"Group Already Exist : {self.get_group_name()}")
                                try:
                                    self.upload()
                                except Exception as e:
                                    self.logger.error(f"Upload '1' {str(e)}")
                                

                    else:
                        if self.email in need_fix:
                            self.logger.info(f"+++ Fixing Errors for {self.browser.email} +++")
                            self.fix_errors()
                            #self.update_email_status(self.browser.email, "active")

                        # Always perform human actions if in human_actions mode, even for fix_errors
                        if "human_actions" in self.actions:
                            self.logger.info("### Performing Human Actions (Fix Errors Mode) ###")
                            self.perform_human_actions_after_login()
                            self.export_ck_lc()
                            self.update_email_status(self.browser.email, "active")
                            self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")

                else:
                    # Only do group operations if NOT in human_actions mode
                    if "human_actions" not in self.actions:
                        #need to change from true to
                        
                        if self.check_groups() == False:
                            try:
                                self.create_groups()
                            except Exception as e:
                                self.browser.logger.error(str(e))
                        else:
                            self.logger.info(f"Group Already Exist : {self.get_group_name()}")
                        try:
                            self.upload()
                        except Exception as e:
                            self.logger.error(f"Upload '2' {str(e)}")
                    else:
                        self.logger.info("### Human Actions Mode - Skipping group operations ###")
                        # Perform human actions even if we're in the else block
                        self.logger.info("### Performing Human Actions ###")
                        self.perform_human_actions_after_login()
                        self.export_ck_lc()
                        self.update_email_status(self.browser.email, "active")
                        self.logger.info(f"### Human Actions Completed for {self.browser.email} ###")


                # Safety check: Don't close browser if still on sign-in page
                current_url = self.browser.this_url()
                self.logger.info(f"DEBUG: Browser running status: {hasattr(self.browser, 'running') and self.browser.running()}")
                self.logger.info(f"DEBUG: Browser object exists: {hasattr(self, 'browser') and self.browser is not None}")

                if any(pattern in current_url for pattern in ['signin', 'accounts.google.com']):
                    self.logger.warning(f"Still on sign-in page: {current_url}")
                    self.logger.warning("Not closing browser - manual intervention may be needed")
                    self.logger.warning("Check if 'login' action is included in the script execution")
                    self.logger.info("DEBUG: Skipping browser.finish() call due to sign-in page detection")
                else:
                    self.logger.info("DEBUG: Calling browser.finish() - not on sign-in page")
                    self.browser.finish()
                    self.terminate_selenium_driver()


            except Exception as e:
                self.logger.error(f"{str(e)}")
                try:
                    # Safety check: Don't close browser if still on sign-in page
                    current_url = self.browser.this_url()
                    if any(pattern in current_url for pattern in ['signin', 'accounts.google.com']):
                        self.logger.warning(f"Exception occurred but still on sign-in page: {current_url}")
                        self.logger.warning("Not closing browser due to exception - manual intervention may be needed")
                    else:
                        self.browser.finish()
                        self.terminate_selenium_driver()
                except Exception as e:
                    pass
                    
        try:
            # Final safety check: Don't close browser if still on sign-in page
            current_url = self.browser.this_url()
            self.logger.info(f"DEBUG: Final cleanup - Browser running: {hasattr(self.browser, 'running') and self.browser.running()}")
            self.logger.info(f"DEBUG: Final cleanup - Current URL: {current_url}")

            if any(pattern in current_url for pattern in ['signin', 'accounts.google.com']):
                self.logger.warning(f"Final cleanup: Still on sign-in page: {current_url}")
                self.logger.warning("Final cleanup: Not closing browser - manual intervention may be needed")
                self.logger.warning("Final cleanup: Check if 'login' action is included in the script execution")
                self.logger.info("DEBUG: Final cleanup - Skipping browser.finish() due to sign-in page")
            else:
                self.logger.info("DEBUG: Final cleanup - Calling browser.finish()")
                self.browser.finish()
                self.terminate_selenium_driver()
        except Exception as e:
            self.logger.error(f"DEBUG: Final cleanup exception: {str(e)}")
            pass



class Main():
    def __init__(self) -> None:
        logging.basicConfig(
            level=logging.INFO,  
            format='[%(asctime)s - %(levelname)s - %(message)s]',
            datefmt='%Y-%m-%d'
        )
        self.logger = logging.getLogger("Main")
        self.logger.info(f"Starting Groups App on User : {os.getlogin()} !!")

        start_time = time.time()

        balance = self.get_balance()
        if balance == "-0.0":
            print("Captcha out of Balance")
        else:
            while True:
                if len(sys.argv) > 1:
                    answ = sys.argv[1]
                else:
                    answ = self.questions()
                if answ == "1":
                    actions = ["login", "create_groups", "upload"]
                    break
                elif answ == "2":
                    actions = ["fix_errors"]
                    break
                elif answ == "3":
                    actions = ["reload_profiles"]
                    break
                elif answ == "4":
                    self.logger.info(f"### Regenerate Data ###")
                    self.create_data_parts("regenerate")
                    print("Press any key to continue...")
                    msvcrt.getch()
                elif answ == "5":
                    self.logger.info(f"### Groups Count : {str(self.count_groups())} ###")
                    print("Press any key to continue...")
                    msvcrt.getch()
                    os.system('cls' if os.name == 'nt' else 'clear')
                elif answ == "6":
                    self.logger.info(f"### Members Count : {str(self.count_members())} ###")
                    print("Press any key to continue...")
                    msvcrt.getch()
                    os.system('cls' if os.name == 'nt' else 'clear')
                elif answ == "7":
                    self.logger.info(f"### {os.getlogin()} Data Count : {str(self.count_data_files())} ###")
                    self.logger.info(f"### {os.getlogin()} Data Files Count : {str(int(self.count_data_files()/90))} ###")
                    return
                elif answ == "8":
                    self.logger.info(f"### Exporting Accounts ###")
                    self.process_dead_accounts()
                    self.logger.info("### Exporting Accounts Done!! ### ")
                    os.system('cls' if os.name == 'nt' else 'clear')
                elif answ == "9":
                    actions = ["login", "human_actions"]
                    break
                else:
                    self.logger.info("Invalid choice, please try again.")
                    if len(sys.argv) > 1:
                        sys.exit(1)

        
        self.create_data_parts()
        worker = Worker(actions)
        worker.create_accounts_map()
        worker.run()

        end_time = time.time()
        execution_time = end_time - start_time
        execution_time_hours = execution_time / 3600
        self.logger.info(f"Script execution time: {execution_time_hours:.2f} hours")



    def get_balance(self):
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        solver = TwoCaptcha(api_key)
        try:
            balance = solver.get_balance()
            balance = float(str(balance)[:4])  
        except Exception as e:
            self.logger.error(f"{str(e)}")
            balance = 0

        return balance

    def count_groups(self):
        with open(map_path, 'r') as file:
            data = json.load(file)
        name_count = 0
        for _, groups in data.items():
            for group in groups:
                if 'name' in group:
                    name_count += 1
        return name_count
    

    def count_data_files(self):
        num_files = len([
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f))
        ])
        return num_files * 90


    def create_data_parts(self, cmd=None):
        if not os.path.exists(data_directory):
            os.makedirs(data_directory)
    
        existing_files = [
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f)) and re.match(r'data_(\d+)\.txt$', f)
        ]

        existing_numbers = set(
            int(re.findall(r'data_(\d+)\.txt', f)[0]) for f in existing_files
        )
    
        if cmd is not None:
            if existing_numbers:
                max_number = max(existing_numbers)
            else:
                max_number = 0
    
            with open(data_file, 'r') as file:
                lines = file.readlines()
    
            total_lines = len(lines)
    
            part_number = max_number + 1
            for i in range(0, total_lines, 90):
                part_file_path = os.path.join(data_directory, f"data_{part_number}.txt")
                with open(part_file_path, 'w') as part_file:
                    part_file.writelines(lines[i:i+90])
                part_number += 1
    
            self.logger.info(f"Generated data files from {max_number + 1} to {part_number - 1}")
        else:
            if not existing_files:
                with open(data_file, 'r') as file:
                    lines = file.readlines()
                    for i in range(0, len(lines), 90):
                        part_file_path = os.path.join(data_directory, f"data_{(i // 90) + 1}.txt")
                        with open(part_file_path, 'w') as part_file:
                            part_file.writelines(lines[i:i+90])
                self.logger.info("Generated data parts.")
            else:
                self.logger.info("### Data parts already exist. Skipping generation. ###")
                total_files = len(existing_files)
                self.logger.info(f"### Data Files N: {total_files * 90} ###")



    def count_members(self):
        with open(map_path, 'r') as file:
            data = json.load(file)
        
        total_members = 0
        for _, groups in data.items():
            for group in groups:
                if 'members_num' in group:
                    total_members += int(group['members_num'])
    
        return total_members


    def clean_file(self, email):
        """Clean profile files for specific email - Enhanced with profile manager support"""
        try:
            # Try to use enhanced driver's deep clean if available
            if hasattr(self, 'browser') and hasattr(self.browser, 'deep_clean_profile'):
                self.browser.deep_clean_profile(email)
                self.logger.info(f"Deep clean completed using enhanced driver for {email}")
            elif hasattr(self, 'browser') and hasattr(self.browser, 'remove_profile'):
                self.browser.remove_profile(email)
                self.logger.info(f"Profile cleaned using enhanced driver for {email}")
            else:
                # Fallback to direct file removal
                profile = f"{profile_home}/{email}"
                if os.path.exists(profile):
                    shutil.rmtree(profile)
                    self.logger.info(f"Profile {profile} removed successfully.")
                else:
                    self.logger.warning(f"Profile {profile} does not exist.")
        except Exception as e:
            self.logger.error(f"Error removing profile for {email}: {str(e)}")

    def deep_clean_profile(self, email):
        """
        Perform deep clean of profile

        Args:
            email (str): Email to deep clean profile for
        """
        try:
            if hasattr(self, 'browser') and hasattr(self.browser, 'deep_clean_profile'):
                return self.browser.deep_clean_profile(email)
            else:
                # Fallback to basic clean
                self.clean_file(email)
                return True
        except Exception as e:
            self.logger.error(f"Error during deep clean for {email}: {str(e)}")
            return False

    def force_clean_all_profiles(self):
        """
        Force clean all profiles - USE WITH CAUTION!
        This will remove all profile data
        """
        try:
            if hasattr(self, 'browser') and hasattr(self.browser, 'force_clean_all_profiles'):
                return self.browser.force_clean_all_profiles()
            else:
                # Fallback to removing profile directory
                if os.path.exists(profile_home):
                    shutil.rmtree(profile_home)
                    os.makedirs(profile_home, exist_ok=True)
                    self.logger.info("Force cleaned all profiles (fallback method)")
                    return True
                return False
        except Exception as e:
            self.logger.error(f"Error during force clean: {str(e)}")
            return False

    def show_profile_locations(self):
        """Show where profiles are stored for debugging"""
        try:
            self.logger.info("=== PROFILE STORAGE LOCATIONS ===")
            self.logger.info(f"Main profiles directory: {profile_home}")
            self.logger.info(f"Profiles directory exists: {os.path.exists(profile_home)}")



            # List existing profiles
            if os.path.exists(profile_home):
                profiles = [d for d in os.listdir(profile_home) if os.path.isdir(os.path.join(profile_home, d))]
                self.logger.info(f"Existing profiles: {len(profiles)}")
                for profile in profiles[:5]:  # Show first 5
                    self.logger.info(f"  - {profile}")
                if len(profiles) > 5:
                    self.logger.info(f"  ... and {len(profiles) - 5} more")

            # Show current profile if available
            if hasattr(self, 'email') and self.email:
                current_profile_path = f"{profile_home}/{self.email}"
                self.logger.info(f"Current profile ({self.email}): {current_profile_path}")
                self.logger.info(f"Current profile exists: {os.path.exists(current_profile_path)}")

            self.logger.info("=== END PROFILE LOCATIONS ===")

        except Exception as e:
            self.logger.error(f"Error showing profile locations: {e}")

    def process_dead_accounts(self):
        with open(dead_accounts, 'r') as file:
            dead_emails = {line.strip() for line in file if line.strip()}
        
        with open(gmail_map_file, 'r') as file:
            gmail_accounts_map = json.load(file)

        matching_accounts = []

        if isinstance(gmail_accounts_map, list):
            updated_accounts_map = []
            for account in gmail_accounts_map:
                email = account.get('email')
                if email in dead_emails:
                    matching_accounts.append(f"{email}:{account.get('password')}")
                    self.clean_file(email)
                else:
                    updated_accounts_map.append(account)
            gmail_accounts_map = updated_accounts_map
        elif isinstance(gmail_accounts_map, dict):
            for email, account_info in gmail_accounts_map.items():
                if email in dead_emails:
                    matching_accounts.append(f"{email}:{account_info['password']}")
            gmail_accounts_map = {email: info for email, info in gmail_accounts_map.items() if email not in dead_emails}
        else:
            raise ValueError("Unexpected JSON structure in Gmail File")

        with open(gmail_map_file, 'w') as file:
            json.dump(gmail_accounts_map, file, indent=4)

        with open("recycled_accounts.txt", 'w') as file:
            for account in matching_accounts:
                file.write(f"{account}\n")

        with open(dead_accounts, 'w') as file:
            file.truncate()


    def questions(self):
        if not os.path.exists(settings_path):
            use_proxy = input("settings.json not found. Do you want to use a proxy? (yes/no): ").strip().lower()
            if use_proxy.lower() in ['y', 'yes']:
                if os.path.exists(proxy_file):
                    with open(proxy_file, 'r') as prx_file:
                        proxies = prx_file.readlines()
                    if proxies:
                        proxy = random.choice(proxies).strip()
                        settings = {
                            'use_proxy': True,
                            'proxy': proxy
                        }
                        with open(settings_path, 'w') as settings_file:
                            json.dump(settings, settings_file, indent=4)
                        self.logger.info("Proxy settings saved to settings.json")
                    else:
                        self.logger.error("proxy.txt is empty.")
                else:
                    self.logger.error("proxy.txt not found.")
            else:
                settings = {
                    'use_proxy': False
                }
                with open(settings_path, 'w') as settings_file:
                    json.dump(settings, settings_file, indent=4)
                self.logger.info("Settings saved to settings.json without proxy")

        self.logger.info("### What do you want to do? ###")
        self.logger.info(""" 1. Login & Create _ Upload""")
        self.logger.info(""" 2. Fix Errors : Like change Phone / Update Settings""")
        self.logger.info(""" 3. Reload Profiles""")
        self.logger.info(""" 4. Regenarate Data Parts""")
        self.logger.info(""" 5. Count Groups Number""")
        self.logger.info(""" 6. Count Group Members""")
        self.logger.info(""" 7. Count Data Files""")
        self.logger.info(""" 8. Export DeadAccounts""")
        self.logger.info(""" 9. Login with Human Actions (Account Warming)""")
        return input("Please enter your choice: ")

Main()

