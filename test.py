import pyautogui
from rapidocr import RapidOCR
import tempfile
import os






def _analyze_screenshot_for_text(target_text):
        """
        Analyze screenshot using RapidOCR to detect specific text

        Args:
            target_text (str): Text to search for in the screenshot

        Returns:
            bool: True if text is found, False otherwise
        """
        try:
            # Initialize RapidOCR
            ocr = RapidOCR()

            # Take screenshot
            #screenshot = pyautogui.screenshot(bbox= (730, 569, 1117, 673))

            # Save to temporary file for OCR processing
            #temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            #screenshot.save(temp_file.name)
            #temp_file.close()

            # Perform OCR - RapidOCR returns a single object, not a tuple
            result = ocr("screens/debug_after_click_Captcha_input_field.png")
            print(result)
            # Clean up temporary file
            #os.unlink(temp_file.name)

            # Handle RapidOCR result format
            if result and hasattr(result, 'txts') and result.txts:
                # Extract all text from OCR results
                detected_text = ' '.join(result.txts)
                print(f"OCR detected text: {detected_text[:200]}...")                
                
                print(f"OCR detected text: {detected_text[:200]}...")

                # Check if target text is present (case-insensitive)
                text_found = target_text.lower() in detected_text.lower()
                if text_found:
                    print(f"Target text '{target_text}' found in screenshot")
                else:
                    print(f"Target text '{target_text}' not found in screenshot")

                return text_found
            elif result and isinstance(result, list):
                # Alternative format: direct list of OCR results
                detected_text = ' '.join([item[1] for item in result if len(item) > 1])
                print(f"OCR detected text: {detected_text[:200]}...")

                # Check if target text is present (case-insensitive)
                text_found = target_text.lower() in detected_text.lower()

                if text_found:
                    print(f"Target text '{target_text}' found in screenshot")
                else:
                    print(f"Target text '{target_text}' not found in screenshot")

                return text_found
            else:
                print("No text detected by OCR or unexpected result format")
                print(f"OCR result type: {type(result)}, content: {str(result)[:100]}")
                return False

        except Exception as e:
            print(f"Error analyzing screenshot with RapidOCR: {str(e)}")
            return False
        

_analyze_screenshot_for_text("Incorrect entry. Please try again.")