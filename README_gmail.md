# Gmail Account Creator - Consolidated Module

A comprehensive, self-contained Gmail account creation automation tool that combines functionality from multiple source files into a single, streamlined module.

## Features

- **Enhanced SeleniumBase Driver**: Advanced browser automation with behavioral simulation and stealth capabilities
- **5sim Phone Verification**: Automated phone number verification using 5sim API
- **API Key Management**: Centralized API key rotation and fallback system
- **CAPTCHA Solving**: Integrated support for TwoCaptcha and AntiCaptcha services
- **Human-like Behavior**: Realistic typing patterns, mouse movements, and interaction timing
- **Configuration Management**: Flexible configuration system with environment variable support
- **Comprehensive Logging**: Detailed logging with Unicode support and log rotation

## Installation

1. Install required dependencies:
```bash
pip install seleniumbase selenium requests pillow numpy opencv-python pyautogui psutil
pip install anticaptchaofficial twocaptcha-python solvecaptcha
```

2. Configure API keys in the script or via environment variables:
   - `TWOCAPTCHA_API_KEY`: For CAPTCHA solving
   - `FIVESIM_API_KEY`: For phone number verification

## Usage

### Command Line Interface

```bash
# Create a single Gmail account
python gmail.py

# Create multiple accounts
python gmail.py --count 5

# Create account with specific first name
python gmail.py --first-name "John"

# Run in headless mode
python gmail.py --headless

# Use custom configuration file
python gmail.py --config config.json
```

### Python API

```python
import gmail

# Create a single account
result = gmail.create_gmail_account(first_name="John")
if result['success']:
    print(f"Email: {result['email']}")
    print(f"Password: {result['account_data']['password']}")

# Create multiple accounts
results = gmail.create_multiple_gmail_accounts(count=3)
for i, result in enumerate(results):
    if result['success']:
        print(f"Account {i+1}: {result['email']}")
```

## Configuration

The module supports various configuration options:

```python
# Update configuration programmatically
gmail.gmail_config.set('headless', True)
gmail.gmail_config.set('use_phone_verification', True)
gmail.gmail_config.set('preferred_countries', ['england', 'canada'])
```

### Configuration File Example

```json
{
    "headless": false,
    "use_phone_verification": true,
    "preferred_countries": ["england", "canada", "germany"],
    "phone_timeout": 300,
    "page_timeout": 100,
    "twocaptcha_api_key": "your_api_key_here",
    "fivesim_api_key": "your_api_key_here"
}
```

## Key Components

### GmailAccountCreator
Main class that orchestrates the entire account creation process.

### EnhancedGmailDriver
Advanced browser driver with stealth capabilities and behavioral simulation.

### PhoneNumberManager
Handles phone number verification using 5sim API.

### BehavioralSimulator
Simulates human-like interactions including realistic typing and mouse movements.

### DataGenerator
Generates realistic account data including names, usernames, and passwords.

### CaptchaSolver
Unified interface for solving various types of CAPTCHAs.

## Output

Created accounts are saved to:
- `credentials/gmail_accounts.txt`: Simple email:password format
- `credentials/gmail_accounts_detailed.json`: Detailed account information

## Logging

Logs are written to `gmail_creator.log` with automatic rotation. The logging system includes:
- Console output with Unicode emoji support
- File logging with detailed debug information
- Automatic log rotation (10MB max, 5 backup files)

## Error Handling

The module includes comprehensive error handling:
- Automatic retry mechanisms
- Graceful degradation when optional features are unavailable
- Detailed error logging and reporting
- Resource cleanup on exit

## Security Features

- Chrome 139.0.0.0 user agent randomization
- Enhanced stealth mode with UC Mode
- Behavioral simulation to avoid detection
- Proxy support (configurable)
- Anti-fingerprinting measures

## Requirements

- Python 3.7+
- Chrome browser
- SeleniumBase
- Valid API keys for phone verification and CAPTCHA solving
- Sufficient balance in 5sim account for phone verification

## Notes

- The module is designed to be self-contained and doesn't require the original source files
- All functionality has been consolidated and optimized for Gmail account creation specifically
- GMX-specific functionality has been removed to focus solely on Gmail
- The module includes fallback mechanisms for when optional dependencies are not available

## Support

For issues or questions, check the logs in `gmail_creator.log` for detailed error information.