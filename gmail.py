"""
Gmail Account Creator - Consolidated Module
Combines functionality from multiple files for Gmail account creation automation.

This module integrates:
- Enhanced SeleniumBase driver with behavioral simulation
- 5sim phone verification system
- API key management
- Configuration management
- Gmail-specific automation workflows
"""

import os
import json
import random
import logging
import time
import threading
import tempfile
import shutil
import string
import secrets
import requests
import base64
from datetime import datetime
from typing import Dict, Optional, Any, List, Tuple
from pathlib import Path
from random import uniform, randint, choice
from time import sleep

# Selenium imports
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException

# SeleniumBase imports
try:
    from seleniumbase import Driver as SBDriver, SB
    SELENIUMBASE_AVAILABLE = True
except ImportError:
    print("WARNING: SeleniumBase not available")
    SBDriver = None
    SB = None
    SELENIUMBASE_AVAILABLE = False

# Additional imports for enhanced functionality
try:
    import psutil
    import numpy as np
    from PIL import Image
    import pyautogui
    import cv2
    ENHANCED_FEATURES_AVAILABLE = True
except ImportError:
    print("WARNING: Some enhanced features not available")
    ENHANCED_FEATURES_AVAILABLE = False

# Captcha solving imports
try:
    from anticaptchaofficial.imagecaptcha import imagecaptcha
    from twocaptcha import TwoCaptcha
    from solvecaptcha import Solvecaptcha
    CAPTCHA_SOLVERS_AVAILABLE = True
except ImportError:
    print("WARNING: Captcha solvers not available")
    CAPTCHA_SOLVERS_AVAILABLE = False

# Configuration
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
Files_home = home
profile_home = f"{home}/Profiles"
settings_path = f"{home}/json/settings.json"
gmail_map_file = f"{home}/credentials/gmail_accounts.txt"
proxy_file = f"{home}/proxy.txt"

# Chrome 139.0.0.0 User Agents
CHROME_139_USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
]

# Gmail-specific XPaths and selectors
GMAIL_SELECTORS = {
    'create_account_button': "//span[contains(text(), 'Create account')]",
    'personal_use_button': "//span[contains(text(), 'For personal use')]",
    'first_name_input': "//input[@name='firstName']",
    'last_name_input': "//input[@name='lastName']",
    'username_input': "//input[@name='Username']",
    'password_input': "//input[@name='Passwd']",
    'confirm_password_input': "//input[@name='ConfirmPasswd']",
    'phone_input': "//input[@name='phoneNumberId']",
    'verification_code_input': "//input[@name='code']",
    'next_button': "//span[contains(text(), 'Next')]",
    'verify_button': "//span[contains(text(), 'Verify')]",
    'captcha_image': "//img[@id='captchaimg']",
    'captcha_input': "//input[@name='ca']",
}

# Captcha detection XPaths
CAPTCHA_XPATHS = [
    "//div[@id='rc-anchor-container']",
    "//div[@id='recaptcha-accessible-status']",
    "//span[contains(@class, 'recaptcha-checkbox')]",
]

# Image CAPTCHA XPaths
IMAGE_CAPTCHA_XPATHS = [
    "//img[@id='captchaimg']",
    "//div[contains(@jscontroller, 'CMcBD')]//img",
    "//input[@name='ca']"
]


class UnicodeStreamHandler(logging.StreamHandler):
    """Custom StreamHandler that handles Unicode characters properly on Windows"""

    def emit(self, record):
        try:
            msg = self.format(record)
            # Replace Unicode emojis with ASCII equivalents for console output
            msg = msg.replace('✅', '[OK]').replace('❌', '[FAIL]').replace('⚠️', '[WARN]')
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)


def setup_logging():
    """Setup centralized logging configuration for Gmail account creation"""
    # Clear any existing handlers to avoid conflicts
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Create file handler with rotation
    from logging.handlers import RotatingFileHandler
    log_file = 'gmail_creator.log'
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # Create console handler with Unicode support
    console_handler = UnicodeStreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Configure root logger
    logging.root.setLevel(logging.DEBUG)
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)

    # Prevent duplicate logs from other libraries
    logging.getLogger('selenium').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)

    return logging.getLogger(__name__)


class GmailConfig:
    """Configuration management for Gmail account creation"""

    def __init__(self):
        self.load_config()

    def load_config(self):
        """Load configuration from various sources"""
        # Default configuration
        self.config = {
            # API Keys
            'twocaptcha_api_key': "d315b270071ccc3922a75b7c56e72da1",
            'solvecaptcha_api_key': "",
            'fivesim_api_key': "eyJhbGciOiJSUzUxMiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.qoIL1qrr274m9daJB_9s1GoG7H4ghpNge2n7Czu6zqwBYw51BRgvTIQO-UOAxLGzJLCRJ3mBgwNh-AS-vl5Apl39N0o4eydIS5d9_x2v0qaqmWoqSjhNqsCU-TkF-vXa04GmZgdP3JZEjvda0id5Up0bSIvluRrEMg4jR9Q0e1vqeaFXLGrcoa3XfjF94SO82YgkgOCkB-1GnZDDvD-J9MD6qBjqowIUroms9o8480qh930lisjNP1E8_-1joexBMkbg5MG-2IoBPIaP6wcJpFqvdMfoqqnaG0BEfUF8kYrVOluuW6Sy8qrnOp7Bq6Q2kRyFO5uUC-dyb4Pf1L1B8w",

            # Browser settings
            'headless': False,
            'use_stealth': True,
            'user_agent': None,  # Will be randomly selected

            # Gmail settings
            'gmail_url': 'https://accounts.google.com/signup',
            'password_length': 14,

            # Phone verification
            'use_phone_verification': True,
            'preferred_countries': ['england', 'canada', 'germany'],
            'phone_timeout': 300,

            # Timeouts
            'page_timeout': 100,
            'navigation_timeout': 50,
            'captcha_timeout': 120,

            # Proxy settings
            'use_proxy': False,
            'proxy_host': '',
            'proxy_port': '',
        }

        # Load from environment variables if available
        self._load_from_env()

        # Load from config file if exists
        config_file = f"{home}/gmail_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    self.config.update(file_config)
            except Exception as e:
                print(f"Warning: Could not load config file: {e}")

    def _load_from_env(self):
        """Load configuration from environment variables"""
        env_mappings = {
            'TWOCAPTCHA_API_KEY': 'twocaptcha_api_key',
            'FIVESIM_API_KEY': 'fivesim_api_key',
            'GMAIL_HEADLESS': 'headless',
            'GMAIL_USE_PROXY': 'use_proxy',
        }

        for env_var, config_key in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                # Convert string booleans
                if value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                self.config[config_key] = value

    def get(self, key, default=None):
        """Get configuration value"""
        return self.config.get(key, default)

    def set(self, key, value):
        """Set configuration value"""
        self.config[key] = value

    def save_config(self):
        """Save current configuration to file"""
        config_file = f"{home}/gmail_config.json"
        try:
            with open(config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            print(f"Warning: Could not save config file: {e}")


class BehavioralSimulator:
    """Human-like behavioral simulation for enhanced stealth"""

    def __init__(self, driver, profile_seed=None):
        """Initialize behavioral simulator with driver and profile-specific seed"""
        self.driver = driver
        self.profile_seed = profile_seed or random.randint(1000, 9999)
        self.rng = random.Random(self.profile_seed)

        # Initialize PyAutoGUI for enhanced mouse movements
        try:
            import pyautogui
            self.pyautogui = pyautogui
            self.pyautogui.FAILSAFE = True
            self.pyautogui.PAUSE = 0.01
            self.pyautogui_available = True
        except ImportError:
            self.pyautogui = None
            self.pyautogui_available = False

        # Behavioral characteristics based on profile seed
        self._init_behavioral_profile()

    def _init_behavioral_profile(self):
        """Initialize profile-specific behavioral characteristics"""
        # Mouse movement characteristics
        self.mouse_speed_base = self.rng.uniform(0.8, 1.5)
        self.mouse_precision = self.rng.uniform(0.7, 0.95)
        self.mouse_overshoot_chance = self.rng.uniform(0.1, 0.3)

        # Typing characteristics
        self.typing_speed_wpm = self.rng.uniform(35, 85)
        self.typing_accuracy = self.rng.uniform(0.92, 0.98)
        self.pause_frequency = self.rng.uniform(0.05, 0.15)

        # Click characteristics
        self.click_duration_base = self.rng.uniform(0.08, 0.15)
        self.pre_click_hover_time = self.rng.uniform(0.2, 0.8)

    def human_type(self, element, text, clear_first=True):
        """Type text with human-like patterns"""
        try:
            if clear_first:
                element.clear()
                time.sleep(self.rng.uniform(0.1, 0.3))

            # Calculate base delay between keystrokes based on WPM
            chars_per_minute = self.typing_speed_wpm * 5
            base_delay = 60.0 / chars_per_minute

            for i, char in enumerate(text):
                # Simulate typing errors occasionally
                if self.rng.random() > self.typing_accuracy and i > 0:
                    # Type wrong character then correct it
                    wrong_char = self.rng.choice('abcdefghijklmnopqrstuvwxyz')
                    element.send_keys(wrong_char)
                    time.sleep(self.rng.uniform(0.2, 0.5))
                    element.send_keys(Keys.BACKSPACE)
                    time.sleep(self.rng.uniform(0.1, 0.2))

                # Type the correct character
                element.send_keys(char)

                # Variable delay based on character type
                if char == ' ':
                    delay = base_delay * self.rng.uniform(1.5, 2.5)
                elif char in '.,!?;:':
                    delay = base_delay * self.rng.uniform(1.2, 2.0)
                elif char.isupper():
                    delay = base_delay * self.rng.uniform(1.1, 1.4)
                else:
                    delay = base_delay * self.rng.uniform(0.7, 1.3)

                # Occasional thinking pauses
                if self.rng.random() < self.pause_frequency:
                    delay += self.rng.uniform(0.5, 2.0)

                time.sleep(delay)

        except Exception:
            # Fallback to simple typing
            try:
                if clear_first:
                    element.clear()
                element.send_keys(text)
            except:
                pass

    def human_click(self, element):
        """Click with human-like timing"""
        try:
            # Pre-click hover time
            hover_time = self.pre_click_hover_time * self.rng.uniform(0.8, 1.2)
            time.sleep(hover_time)

            # Click the element
            element.click()

            # Post-click delay
            post_click_delay = self.rng.uniform(0.1, 0.3)
            time.sleep(post_click_delay)

        except Exception:
            # Fallback to simple click
            try:
                element.click()
            except:
                pass


class EnhancedGmailDriver:
    """Enhanced SeleniumBase driver specifically for Gmail account creation"""

    @staticmethod
    def get_random_user_agent():
        """Get a random Chrome 139.0.0.0 user agent"""
        return random.choice(CHROME_139_USER_AGENTS)

    def __init__(self, first_name=None, force_fullscreen=False):
        """Initialize the enhanced Gmail driver"""
        self.first_name = first_name
        self.force_fullscreen = force_fullscreen
        self.logger = logging.getLogger(f"GmailDriver")

        # Initialize browser with enhanced stealth
        self.browser = self._create_enhanced_browser()

        # Initialize behavioral simulator
        profile_seed = hash(first_name or 'default') % 10000
        self.behavioral_simulator = BehavioralSimulator(self.browser, profile_seed)

    def _create_enhanced_browser(self):
        """Create enhanced browser with SeleniumBase"""
        try:
            self.logger.info("Creating enhanced browser for Gmail")

            if not SELENIUMBASE_AVAILABLE:
                raise Exception("SeleniumBase not available")

            # Get random user agent
            random_user_agent = self.get_random_user_agent()
            self.logger.info(f"Selected user agent: {random_user_agent[:80]}...")

            # Create SeleniumBase driver with UC Mode
            driver_kwargs = {
                'browser': 'chrome',
                'headless': gmail_config.get('headless', False),
                'uc': True,  # Enable UC Mode for stealth
                'agent': random_user_agent,
                'disable_csp': True,
                'disable_ws': True,
            }

            # Create the driver
            driver = SBDriver(**driver_kwargs)
            self.logger.info("Enhanced Gmail driver created successfully")

            # Set timeouts
            try:
                driver.set_page_load_timeout(gmail_config.get('page_timeout', 100))
                driver.implicitly_wait(30)
            except Exception as e:
                self.logger.warning(f"Could not set timeouts: {e}")

            return driver

        except Exception as e:
            self.logger.error(f"Failed to create enhanced browser: {e}")
            raise e

    def navigate_to_gmail_signup(self):
        """Navigate to Gmail signup page"""
        try:
            gmail_url = gmail_config.get('gmail_url', 'https://accounts.google.com/signup')
            self.logger.info(f"Navigating to Gmail signup: {gmail_url}")
            self.browser.get(gmail_url)
            time.sleep(uniform(2, 4))
            return True
        except Exception as e:
            self.logger.error(f"Failed to navigate to Gmail signup: {e}")
            return False

    def find_element_safe(self, selector, timeout=10):
        """Safely find element with timeout"""
        try:
            element = WebDriverWait(self.browser, timeout).until(
                EC.element_to_be_clickable((By.XPATH, selector))
            )
            return element
        except TimeoutException:
            self.logger.debug(f"Element not found: {selector}")
            return None
        except Exception as e:
            self.logger.debug(f"Error finding element {selector}: {e}")
            return None

    def click_element_safe(self, selector, timeout=10):
        """Safely click element with behavioral simulation"""
        try:
            element = self.find_element_safe(selector, timeout)
            if element:
                self.behavioral_simulator.human_click(element)
                return True
            return False
        except Exception as e:
            self.logger.debug(f"Error clicking element {selector}: {e}")
            return False

    def type_text_safe(self, selector, text, timeout=10, clear_first=True):
        """Safely type text with behavioral simulation"""
        try:
            element = self.find_element_safe(selector, timeout)
            if element:
                self.behavioral_simulator.human_type(element, text, clear_first)
                return True
            return False
        except Exception as e:
            self.logger.debug(f"Error typing text to {selector}: {e}")
            return False

    def detect_captcha(self):
        """Detect if CAPTCHA is present"""
        for xpath in CAPTCHA_XPATHS:
            if self.find_element_safe(xpath, timeout=2):
                self.logger.warning(f"CAPTCHA detected: {xpath}")
                return True
        return False

    def detect_image_captcha(self):
        """Detect if image CAPTCHA is present"""
        for xpath in IMAGE_CAPTCHA_XPATHS:
            element = self.find_element_safe(xpath, timeout=2)
            if element and element.is_displayed():
                self.logger.warning(f"Image CAPTCHA detected: {xpath}")
                return True
        return False

    def quit(self):
        """Safely quit the browser"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.browser.quit()
                self.logger.info("Browser quit successfully")
        except Exception as e:
            self.logger.warning(f"Error quitting browser: {e}")

    def __del__(self):
        """Cleanup when driver is destroyed"""
        self.quit()


class FiveSimError(Exception):
    """Custom exception for 5sim API errors"""
    pass


class SMSMessage:
    """SMS message data structure"""
    def __init__(self, id, created_at, date, sender, text, code):
        self.id = id
        self.created_at = created_at
        self.date = date
        self.sender = sender
        self.text = text
        self.code = code


class PhoneOrder:
    """Phone number order data structure"""
    def __init__(self, id, phone, operator, product, price, status, expires, sms, created_at, country):
        self.id = id
        self.phone = phone
        self.operator = operator
        self.product = product
        self.price = price
        self.status = status
        self.expires = expires
        self.sms = sms
        self.created_at = created_at
        self.country = country


class FiveSimClient:
    """5sim API client for phone number verification"""

    def __init__(self, api_key: str, logger: Optional[logging.Logger] = None):
        """Initialize 5sim client"""
        if not api_key or not api_key.strip():
            raise ValueError("API key is required and cannot be empty")

        self.api_key = api_key.strip()
        self.base_url = "https://5sim.net/v1"
        self.logger = logger or logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # Configuration
        self.default_country = "england"
        self.default_operator = "any"
        self.product = "google"  # For Gmail verification
        self.max_wait_time = 300
        self.check_interval = 10

    def get_balance(self) -> float:
        """Get account balance"""
        try:
            url = f"{self.base_url}/user/profile"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            data = response.json()
            balance = data.get('balance', 0)
            self.logger.info(f"5sim account balance: ${balance}")
            return balance
        except Exception as e:
            self.logger.error(f"Failed to get 5sim balance: {e}")
            raise FiveSimError(f"Failed to get balance: {e}")

    def buy_phone_number(self, country: str = None, operator: str = None) -> PhoneOrder:
        """Purchase a phone number for Gmail verification"""
        try:
            country = country or self.default_country
            operator = operator or self.default_operator

            self.logger.info(f"Attempting to buy phone number from {country}")

            # Check balance first
            balance = self.get_balance()
            if balance < 0.5:
                raise FiveSimError(f"Insufficient balance: ${balance}")

            # Purchase number
            url = f"{self.base_url}/user/buy/activation/{country}/{operator}/{self.product}"
            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            data = response.json()

            # Parse SMS messages
            sms_list = []
            for sms_data in data.get('sms', []) or []:
                if sms_data:
                    sms_list.append(SMSMessage(
                        id=sms_data.get('id', 0),
                        created_at=sms_data.get('created_at', ''),
                        date=sms_data.get('date', ''),
                        sender=sms_data.get('sender', ''),
                        text=sms_data.get('text', ''),
                        code=sms_data.get('code', '')
                    ))

            order = PhoneOrder(
                id=data['id'],
                phone=data['phone'],
                operator=data['operator'],
                product=data['product'],
                price=data['price'],
                status=data['status'],
                expires=data['expires'],
                sms=sms_list,
                created_at=data['created_at'],
                country=data['country']
            )

            self.logger.info(f"Successfully purchased phone number: {order.phone} (Order ID: {order.id})")
            return order

        except Exception as e:
            self.logger.error(f"Failed to buy phone number: {e}")
            raise FiveSimError(f"Failed to buy phone number: {e}")

    def check_sms(self, order_id: int) -> PhoneOrder:
        """Check for SMS messages on a phone number order"""
        try:
            url = f"{self.base_url}/user/check/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()

            data = response.json()

            # Parse SMS messages
            sms_list = []
            for sms_data in data.get('sms', []) or []:
                if sms_data:
                    sms_list.append(SMSMessage(
                        id=sms_data.get('id', 0),
                        created_at=sms_data.get('created_at', ''),
                        date=sms_data.get('date', ''),
                        sender=sms_data.get('sender', ''),
                        text=sms_data.get('text', ''),
                        code=sms_data.get('code', '')
                    ))

            order = PhoneOrder(
                id=data['id'],
                phone=data['phone'],
                operator=data['operator'],
                product=data['product'],
                price=data['price'],
                status=data['status'],
                expires=data['expires'],
                sms=sms_list,
                created_at=data['created_at'],
                country=data['country']
            )

            return order

        except Exception as e:
            self.logger.error(f"Failed to check SMS for order {order_id}: {e}")
            raise FiveSimError(f"Failed to check SMS: {e}")

    def wait_for_sms(self, order_id: int, timeout: int = None) -> Optional[str]:
        """Wait for SMS verification code"""
        timeout = timeout or self.max_wait_time
        start_time = time.time()

        self.logger.info(f"Starting SMS polling for order {order_id} with {timeout}s timeout")

        while time.time() - start_time < timeout:
            try:
                order = self.check_sms(order_id)

                # Check if we have SMS messages
                if order.sms:
                    for sms in order.sms:
                        if sms.code:
                            self.logger.info(f"Received SMS verification code: {sms.code}")
                            return sms.code
                        elif sms.text:
                            code = self._extract_verification_code(sms.text)
                            if code:
                                self.logger.info(f"Extracted verification code: {code}")
                                return code

                time.sleep(self.check_interval)

            except Exception as e:
                self.logger.error(f"Error while waiting for SMS: {e}")
                time.sleep(5)

        self.logger.warning(f"SMS verification code not received within {timeout} seconds")
        return None

    def _extract_verification_code(self, sms_text: str) -> Optional[str]:
        """Extract verification code from SMS text"""
        import re

        patterns = [
            r'G-(\d{6})',  # Google format "G-123456"
            r'(?:verification code|code)[:\s]*(\d{6})',
            r'(\d{6})',  # Standard 6-digit code
        ]

        for pattern in patterns:
            match = re.search(pattern, sms_text, re.IGNORECASE)
            if match:
                code = match.group(1) if match.groups() else match.group(0)
                if code.isdigit() and 4 <= len(code) <= 8:
                    return code

        return None

    def finish_order(self, order_id: int) -> bool:
        """Mark order as finished (successful verification)"""
        try:
            url = f"{self.base_url}/user/finish/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()

            self.logger.info(f"Successfully finished order {order_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to finish order {order_id}: {e}")
            return False

    def cancel_order(self, order_id: int) -> bool:
        """Cancel an order (if no SMS received)"""
        try:
            url = f"{self.base_url}/user/cancel/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()

            self.logger.info(f"Successfully canceled order {order_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False


class PhoneNumberManager:
    """High-level manager for phone number verification"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.client = None
        self.current_order = None

        # Initialize 5sim client
        api_key = gmail_config.get('fivesim_api_key')
        if api_key:
            try:
                self.client = FiveSimClient(api_key, self.logger)
            except Exception as e:
                self.logger.error(f"Failed to initialize 5sim client: {e}")

    def get_phone_number(self, country: str = 'england') -> Optional[str]:
        """Get a phone number for Gmail verification"""
        if not self.client:
            self.logger.error("5sim client not initialized")
            return None

        try:
            self.logger.info(f"Attempting to get phone number from {country}")
            order = self.client.buy_phone_number(country=country)
            self.current_order = order
            return order.phone
        except Exception as e:
            self.logger.error(f"Failed to get phone number: {e}")
            return None

    def wait_for_verification_code(self, timeout: int = 300) -> Optional[str]:
        """Wait for SMS verification code"""
        if not self.current_order or not self.client:
            self.logger.error("No active order or client not initialized")
            return None

        try:
            code = self.client.wait_for_sms(self.current_order.id, timeout)
            return code
        except Exception as e:
            self.logger.error(f"Error waiting for verification code: {e}")
            return None

    def finish_current_order(self) -> bool:
        """Mark the current order as finished"""
        if not self.current_order or not self.client:
            return False

        try:
            success = self.client.finish_order(self.current_order.id)
            if success:
                self.current_order = None
            return success
        except Exception as e:
            self.logger.error(f"Error finishing order: {e}")
            return False

    def cancel_current_order(self) -> bool:
        """Cancel the current order"""
        if not self.current_order or not self.client:
            return False

        try:
            success = self.client.cancel_order(self.current_order.id)
            if success:
                self.current_order = None
            return success
        except Exception as e:
            self.logger.error(f"Error canceling order: {e}")
            return False


class APIKeyManager:
    """Centralized API key manager with rotation and fallback"""

    def __init__(self, api_keys: Optional[List[str]] = None):
        self.api_keys = api_keys or []
        self.current_key_index = 0
        self._lock = threading.Lock()
        self.logger = logging.getLogger("APIKeyManager")

        # Track key performance
        self.key_stats = {i: {"success": 0, "failures": 0, "last_used": 0}
                         for i in range(len(self.api_keys))}

    def get_current_api_key(self) -> str:
        """Get the current API key (thread-safe)"""
        with self._lock:
            if not self.api_keys:
                return ""
            return self.api_keys[self.current_key_index]

    def rotate_api_key(self) -> None:
        """Rotate to the next API key (thread-safe)"""
        with self._lock:
            if self.api_keys:
                self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
                self.logger.info(f"Rotated to API key #{self.current_key_index + 1}")

    def update_key_stats(self, key_index: int, success: bool) -> None:
        """Update statistics for a specific key"""
        with self._lock:
            if key_index in self.key_stats:
                if success:
                    self.key_stats[key_index]["success"] += 1
                else:
                    self.key_stats[key_index]["failures"] += 1
                self.key_stats[key_index]["last_used"] = time.time()

    def make_request(self, endpoint: str, method: str = "POST",
                    payload: Optional[Dict[str, Any]] = None,
                    base_url: str = "https://api.example.com",
                    timeout: int = 30) -> Optional[Dict[str, Any]]:
        """Make API request with key rotation and fallback"""
        if not self.api_keys:
            self.logger.error("No API keys available")
            return None

        max_retries = len(self.api_keys)

        for attempt in range(max_retries):
            current_key = self.get_current_api_key()

            headers = {
                "Authorization": f"Bearer {current_key}",
                "Content-Type": "application/json"
            }

            try:
                self.logger.info(f"Using API key #{self.current_key_index + 1} (attempt {attempt + 1})")

                url = f"{base_url}{endpoint}"
                if method.upper() == "GET":
                    response = requests.get(url, headers=headers, timeout=timeout)
                elif method.upper() == "POST":
                    response = requests.post(url, headers=headers, json=payload, timeout=timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                if response.status_code == 200:
                    self.update_key_stats(self.current_key_index, True)
                    self.rotate_api_key()
                    return response.json()
                else:
                    self.logger.warning(f"API returned status {response.status_code}")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    continue

            except Exception as e:
                self.logger.error(f"API request failed: {e}")
                self.update_key_stats(self.current_key_index, False)
                self.rotate_api_key()
                continue

        self.logger.error("All API key attempts failed")
        return None

    def add_api_key(self, api_key: str) -> None:
        """Add a new API key"""
        with self._lock:
            if api_key not in self.api_keys:
                self.api_keys.append(api_key)
                new_index = len(self.api_keys) - 1
                self.key_stats[new_index] = {"success": 0, "failures": 0, "last_used": 0}
                self.logger.info(f"Added new API key (total: {len(self.api_keys)})")


class CaptchaSolver:
    """Unified captcha solving interface"""

    def __init__(self):
        self.logger = logging.getLogger("CaptchaSolver")
        self.twocaptcha_key = gmail_config.get('twocaptcha_api_key')
        self.solvecaptcha_key = gmail_config.get('solvecaptcha_api_key')

    def solve_image_captcha(self, image_path: str) -> Optional[str]:
        """Solve image CAPTCHA using available services"""
        if not CAPTCHA_SOLVERS_AVAILABLE:
            self.logger.error("No captcha solvers available")
            return None

        # Try TwoCaptcha first
        if self.twocaptcha_key:
            try:
                solver = TwoCaptcha(self.twocaptcha_key)
                result = solver.normal(image_path)
                if result and result.get('code'):
                    self.logger.info("CAPTCHA solved with TwoCaptcha")
                    return result['code']
            except Exception as e:
                self.logger.warning(f"TwoCaptcha failed: {e}")

        # Try AntiCaptcha as fallback
        try:
            solver = imagecaptcha()
            solver.set_key(self.twocaptcha_key)
            result = solver.solve_and_return_solution(image_path)
            if result and result != 0:
                self.logger.info("CAPTCHA solved with AntiCaptcha")
                return result
        except Exception as e:
            self.logger.warning(f"AntiCaptcha failed: {e}")

        self.logger.error("All captcha solving attempts failed")
        return None

    def extract_image_captcha(self, driver) -> Optional[str]:
        """Extract CAPTCHA image from page"""
        try:
            # Find CAPTCHA image element
            captcha_img = None
            for xpath in IMAGE_CAPTCHA_XPATHS:
                try:
                    element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, xpath))
                    )
                    if element and element.is_displayed():
                        captcha_img = element
                        break
                except:
                    continue

            if not captcha_img:
                return None

            # Get image source
            img_src = captcha_img.get_attribute('src')
            if not img_src:
                return None

            # Handle data URLs (base64 encoded images)
            if img_src.startswith('data:image'):
                header, data = img_src.split(',', 1)
                image_data = base64.b64decode(data)

                # Save to temporary file
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                temp_file.write(image_data)
                temp_file.close()

                return temp_file.name

            # Handle regular URLs
            else:
                response = requests.get(img_src, timeout=10)
                response.raise_for_status()

                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                temp_file.write(response.content)
                temp_file.close()

                return temp_file.name

        except Exception as e:
            self.logger.error(f"Error extracting CAPTCHA image: {e}")
            return None


class DataGenerator:
    """Generate realistic data for Gmail account creation"""

    def __init__(self):
        self.logger = logging.getLogger("DataGenerator")

        # Name lists for realistic generation
        self.first_names = [
            "James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda",
            "William", "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica",
            "Thomas", "Sarah", "Christopher", "Karen", "Charles", "Nancy", "Daniel", "Lisa",
            "Matthew", "Betty", "Anthony", "Helen", "Mark", "Sandra", "Donald", "Donna",
            "Steven", "Carol", "Paul", "Ruth", "Andrew", "Sharon", "Joshua", "Michelle",
            "Kenneth", "Laura", "Kevin", "Sarah", "Brian", "Kimberly", "George", "Deborah",
            "Edward", "Dorothy", "Ronald", "Lisa", "Timothy", "Nancy", "Jason", "Karen",
            "Jeffrey", "Betty", "Ryan", "Helen", "Jacob", "Sandra", "Gary", "Donna",
            "Nicholas", "Carol", "Eric", "Ruth", "Jonathan", "Sharon", "Stephen", "Michelle",
            "Larry", "Laura", "Justin", "Sarah", "Scott", "Kimberly", "Brandon", "Deborah",
            "Benjamin", "Dorothy", "Samuel", "Amy", "Gregory", "Angela", "Alexander", "Ashley",
            "Patrick", "Brenda", "Frank", "Emma", "Raymond", "Olivia", "Jack", "Cynthia",
            "Dennis", "Marie", "Jerry", "Janet", "Tyler", "Catherine", "Aaron", "Frances",
            "Jose", "Christine", "Henry", "Samantha", "Adam", "Debra", "Douglas", "Rachel",
            "Nathan", "Carolyn", "Peter", "Janet", "Zachary", "Virginia", "Kyle", "Maria"
        ]

        self.last_names = [
            "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
            "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas",
            "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White",
            "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker", "Young",
            "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
            "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell",
            "Carter", "Roberts", "Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker",
            "Cruz", "Edwards", "Collins", "Reyes", "Stewart", "Morris", "Morales", "Murphy",
            "Cook", "Rogers", "Gutierrez", "Ortiz", "Morgan", "Cooper", "Peterson", "Bailey",
            "Reed", "Kelly", "Howard", "Ramos", "Kim", "Cox", "Ward", "Richardson",
            "Watson", "Brooks", "Chavez", "Wood", "James", "Bennett", "Gray", "Mendoza",
            "Ruiz", "Hughes", "Price", "Alvarez", "Castillo", "Sanders", "Patel", "Myers",
            "Long", "Ross", "Foster", "Jimenez", "Powell", "Jenkins", "Perry", "Russell",
            "Sullivan", "Bell", "Coleman", "Butler", "Henderson", "Barnes", "Gonzales", "Fisher",
            "Vasquez", "Simmons", "Romero", "Jordan", "Patterson", "Alexander", "Hamilton", "Graham"
        ]

    def generate_name(self) -> Tuple[str, str]:
        """Generate a random first and last name"""
        first_name = random.choice(self.first_names)
        last_name = random.choice(self.last_names)
        return first_name, last_name

    def generate_username(self, first_name: str, last_name: str) -> str:
        """Generate a username based on name"""
        patterns = [
            f"{first_name.lower()}.{last_name.lower()}",
            f"{first_name.lower()}{last_name.lower()}",
            f"{first_name.lower()}{last_name.lower()}{random.randint(1, 999)}",
            f"{first_name.lower()}.{last_name.lower()}{random.randint(1, 99)}",
            f"{first_name[0].lower()}{last_name.lower()}",
            f"{first_name.lower()}{last_name[0].lower()}{random.randint(10, 999)}",
        ]
        return random.choice(patterns)

    def generate_password(self, length: int = 14) -> str:
        """Generate a secure password"""
        # Ensure we have at least one of each required character type
        password_chars = []

        # Add required character types
        password_chars.append(random.choice(string.ascii_lowercase))
        password_chars.append(random.choice(string.ascii_uppercase))
        password_chars.append(random.choice(string.digits))
        password_chars.append(random.choice("!@#$%^&*"))

        # Fill the rest with random characters
        all_chars = string.ascii_letters + string.digits + "!@#$%^&*"
        for _ in range(length - 4):
            password_chars.append(random.choice(all_chars))

        # Shuffle the password
        random.shuffle(password_chars)
        return ''.join(password_chars)

    def generate_birth_date(self) -> Tuple[int, int, int]:
        """Generate a realistic birth date (18-65 years old)"""
        current_year = datetime.now().year
        birth_year = random.randint(current_year - 65, current_year - 18)
        birth_month = random.randint(1, 12)

        # Handle different month lengths
        if birth_month in [1, 3, 5, 7, 8, 10, 12]:
            max_day = 31
        elif birth_month in [4, 6, 9, 11]:
            max_day = 30
        else:  # February
            if birth_year % 4 == 0 and (birth_year % 100 != 0 or birth_year % 400 == 0):
                max_day = 29
            else:
                max_day = 28

        birth_day = random.randint(1, max_day)
        return birth_month, birth_day, birth_year


# Initialize global configuration and components
gmail_config = GmailConfig()
data_generator = DataGenerator()
captcha_solver = CaptchaSolver()


class GmailAccountCreator:
    """Main Gmail account creation workflow"""

    def __init__(self, first_name: str = None):
        """Initialize Gmail account creator"""
        self.first_name = first_name
        self.logger = setup_logging()
        self.driver = None
        self.phone_manager = PhoneNumberManager(self.logger)

        # Account data
        self.account_data = {}

        # Initialize driver
        try:
            self.driver = EnhancedGmailDriver(first_name=first_name)
            self.logger.info("Gmail account creator initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Gmail account creator: {e}")
            raise e

    def create_account(self) -> Dict[str, Any]:
        """Create a Gmail account with full automation"""
        try:
            self.logger.info("Starting Gmail account creation process")

            # Step 1: Generate account data
            if not self._generate_account_data():
                return {"success": False, "error": "Failed to generate account data"}

            # Step 2: Navigate to Gmail signup
            if not self.driver.navigate_to_gmail_signup():
                return {"success": False, "error": "Failed to navigate to Gmail signup"}

            # Step 3: Fill basic information
            if not self._fill_basic_info():
                return {"success": False, "error": "Failed to fill basic information"}

            # Step 4: Handle phone verification
            if gmail_config.get('use_phone_verification', True):
                if not self._handle_phone_verification():
                    return {"success": False, "error": "Failed phone verification"}

            # Step 5: Complete account setup
            if not self._complete_account_setup():
                return {"success": False, "error": "Failed to complete account setup"}

            # Step 6: Save account data
            self._save_account_data()

            self.logger.info("Gmail account created successfully!")
            return {
                "success": True,
                "account_data": self.account_data,
                "email": f"{self.account_data['username']}@gmail.com"
            }

        except Exception as e:
            self.logger.error(f"Gmail account creation failed: {e}")
            return {"success": False, "error": str(e)}
        finally:
            self._cleanup()

    def _generate_account_data(self) -> bool:
        """Generate realistic account data"""
        try:
            # Generate name
            if self.first_name:
                first_name = self.first_name
                _, last_name = data_generator.generate_name()
            else:
                first_name, last_name = data_generator.generate_name()

            # Generate other data
            username = data_generator.generate_username(first_name, last_name)
            password = data_generator.generate_password(gmail_config.get('password_length', 14))
            birth_month, birth_day, birth_year = data_generator.generate_birth_date()

            self.account_data = {
                'first_name': first_name,
                'last_name': last_name,
                'username': username,
                'password': password,
                'birth_month': birth_month,
                'birth_day': birth_day,
                'birth_year': birth_year,
                'phone_number': None,
                'verification_code': None,
                'created_at': datetime.now().isoformat()
            }

            self.logger.info(f"Generated account data for: {first_name} {last_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to generate account data: {e}")
            return False

    def _fill_basic_info(self) -> bool:
        """Fill basic account information"""
        try:
            self.logger.info("Filling basic account information")

            # Click "Create account" if present
            if self.driver.find_element_safe(GMAIL_SELECTORS['create_account_button']):
                self.driver.click_element_safe(GMAIL_SELECTORS['create_account_button'])
                time.sleep(uniform(1, 2))

            # Click "For personal use" if present
            if self.driver.find_element_safe(GMAIL_SELECTORS['personal_use_button']):
                self.driver.click_element_safe(GMAIL_SELECTORS['personal_use_button'])
                time.sleep(uniform(1, 2))

            # Fill first name
            if not self.driver.type_text_safe(GMAIL_SELECTORS['first_name_input'],
                                            self.account_data['first_name']):
                self.logger.error("Failed to fill first name")
                return False

            # Fill last name
            if not self.driver.type_text_safe(GMAIL_SELECTORS['last_name_input'],
                                            self.account_data['last_name']):
                self.logger.error("Failed to fill last name")
                return False

            # Fill username
            if not self.driver.type_text_safe(GMAIL_SELECTORS['username_input'],
                                            self.account_data['username']):
                self.logger.error("Failed to fill username")
                return False

            # Fill password
            if not self.driver.type_text_safe(GMAIL_SELECTORS['password_input'],
                                            self.account_data['password']):
                self.logger.error("Failed to fill password")
                return False

            # Confirm password
            if not self.driver.type_text_safe(GMAIL_SELECTORS['confirm_password_input'],
                                            self.account_data['password']):
                self.logger.error("Failed to confirm password")
                return False

            # Click Next
            if not self.driver.click_element_safe(GMAIL_SELECTORS['next_button']):
                self.logger.error("Failed to click Next button")
                return False

            time.sleep(uniform(2, 4))
            self.logger.info("Basic information filled successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to fill basic info: {e}")
            return False

    def _handle_phone_verification(self) -> bool:
        """Handle phone number verification"""
        try:
            self.logger.info("Starting phone verification process")

            # Check if phone input is present
            phone_input = self.driver.find_element_safe(GMAIL_SELECTORS['phone_input'], timeout=10)
            if not phone_input:
                self.logger.info("Phone verification not required")
                return True

            # Get phone number from 5sim
            preferred_countries = gmail_config.get('preferred_countries', ['england'])
            phone_number = None

            for country in preferred_countries:
                phone_number = self.phone_manager.get_phone_number(country)
                if phone_number:
                    break

            if not phone_number:
                self.logger.error("Failed to get phone number")
                return False

            self.account_data['phone_number'] = phone_number
            self.logger.info(f"Got phone number: {phone_number}")

            # Enter phone number
            if not self.driver.type_text_safe(GMAIL_SELECTORS['phone_input'], phone_number):
                self.logger.error("Failed to enter phone number")
                return False

            # Click Next/Verify
            if not self.driver.click_element_safe(GMAIL_SELECTORS['next_button']):
                if not self.driver.click_element_safe(GMAIL_SELECTORS['verify_button']):
                    self.logger.error("Failed to click verification button")
                    return False

            time.sleep(uniform(3, 5))

            # Wait for SMS verification code
            timeout = gmail_config.get('phone_timeout', 300)
            verification_code = self.phone_manager.wait_for_verification_code(timeout)

            if not verification_code:
                self.logger.error("Failed to receive verification code")
                self.phone_manager.cancel_current_order()
                return False

            self.account_data['verification_code'] = verification_code
            self.logger.info(f"Received verification code: {verification_code}")

            # Enter verification code
            code_input = self.driver.find_element_safe(GMAIL_SELECTORS['verification_code_input'], timeout=10)
            if code_input:
                if not self.driver.type_text_safe(GMAIL_SELECTORS['verification_code_input'],
                                                verification_code):
                    self.logger.error("Failed to enter verification code")
                    return False

                # Click Next/Verify
                if not self.driver.click_element_safe(GMAIL_SELECTORS['next_button']):
                    if not self.driver.click_element_safe(GMAIL_SELECTORS['verify_button']):
                        self.logger.error("Failed to submit verification code")
                        return False

                time.sleep(uniform(2, 4))

            # Mark phone order as finished
            self.phone_manager.finish_current_order()

            self.logger.info("Phone verification completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Phone verification failed: {e}")
            self.phone_manager.cancel_current_order()
            return False

    def _complete_account_setup(self) -> bool:
        """Complete the account setup process"""
        try:
            self.logger.info("Completing account setup")

            # Handle any remaining steps (birth date, recovery info, etc.)
            max_attempts = 10
            attempt = 0

            while attempt < max_attempts:
                attempt += 1

                # Check for CAPTCHA
                if self.driver.detect_captcha() or self.driver.detect_image_captcha():
                    if not self._handle_captcha():
                        self.logger.error("Failed to solve CAPTCHA")
                        return False

                # Look for Next button
                if self.driver.click_element_safe(GMAIL_SELECTORS['next_button'], timeout=5):
                    time.sleep(uniform(2, 4))
                    continue

                # Check if we've reached the final page
                current_url = self.driver.browser.current_url
                if 'myaccount.google.com' in current_url or 'accounts.google.com/ManageAccount' in current_url:
                    self.logger.info("Account setup completed - reached account management page")
                    return True

                # Check for Gmail inbox
                if 'mail.google.com' in current_url:
                    self.logger.info("Account setup completed - reached Gmail inbox")
                    return True

                time.sleep(uniform(1, 2))

            self.logger.warning("Account setup may be incomplete, but proceeding")
            return True

        except Exception as e:
            self.logger.error(f"Failed to complete account setup: {e}")
            return False

    def _handle_captcha(self) -> bool:
        """Handle CAPTCHA if present"""
        try:
            self.logger.info("Attempting to solve CAPTCHA")

            # Extract CAPTCHA image
            image_path = captcha_solver.extract_image_captcha(self.driver.browser)
            if not image_path:
                self.logger.error("Failed to extract CAPTCHA image")
                return False

            # Solve CAPTCHA
            solution = captcha_solver.solve_image_captcha(image_path)
            if not solution:
                self.logger.error("Failed to solve CAPTCHA")
                return False

            # Enter CAPTCHA solution
            if not self.driver.type_text_safe(GMAIL_SELECTORS['captcha_input'], solution):
                self.logger.error("Failed to enter CAPTCHA solution")
                return False

            # Clean up temporary file
            try:
                os.unlink(image_path)
            except:
                pass

            self.logger.info("CAPTCHA solved successfully")
            return True

        except Exception as e:
            self.logger.error(f"CAPTCHA handling failed: {e}")
            return False

    def _save_account_data(self):
        """Save account data to file"""
        try:
            # Ensure credentials directory exists
            credentials_dir = f"{home}/credentials"
            os.makedirs(credentials_dir, exist_ok=True)

            # Save to Gmail accounts file
            gmail_accounts_file = f"{credentials_dir}/gmail_accounts.txt"
            account_line = f"{self.account_data['username']}@gmail.com:{self.account_data['password']}\n"

            with open(gmail_accounts_file, 'a', encoding='utf-8') as f:
                f.write(account_line)

            # Save detailed data to JSON
            detailed_file = f"{credentials_dir}/gmail_accounts_detailed.json"
            detailed_data = []

            if os.path.exists(detailed_file):
                try:
                    with open(detailed_file, 'r', encoding='utf-8') as f:
                        detailed_data = json.load(f)
                except:
                    detailed_data = []

            detailed_data.append(self.account_data)

            with open(detailed_file, 'w', encoding='utf-8') as f:
                json.dump(detailed_data, f, indent=2, ensure_ascii=False)

            self.logger.info("Account data saved successfully")

        except Exception as e:
            self.logger.error(f"Failed to save account data: {e}")

    def _cleanup(self):
        """Cleanup resources"""
        try:
            if self.driver:
                self.driver.quit()
            self.logger.info("Cleanup completed")
        except Exception as e:
            self.logger.warning(f"Cleanup error: {e}")

    def __del__(self):
        """Destructor"""
        self._cleanup()


def create_gmail_account(first_name: str = None) -> Dict[str, Any]:
    """
    Convenience function to create a Gmail account

    Args:
        first_name: Optional first name to use for the account

    Returns:
        Dictionary with creation result and account data
    """
    creator = GmailAccountCreator(first_name=first_name)
    return creator.create_account()


def create_multiple_gmail_accounts(count: int, first_names: List[str] = None) -> List[Dict[str, Any]]:
    """
    Create multiple Gmail accounts

    Args:
        count: Number of accounts to create
        first_names: Optional list of first names to use

    Returns:
        List of creation results
    """
    results = []
    logger = setup_logging()

    for i in range(count):
        try:
            first_name = None
            if first_names and i < len(first_names):
                first_name = first_names[i]

            logger.info(f"Creating Gmail account {i+1}/{count}")
            result = create_gmail_account(first_name=first_name)
            results.append(result)

            if result['success']:
                logger.info(f"Account {i+1} created successfully: {result.get('email', 'N/A')}")
            else:
                logger.error(f"Account {i+1} creation failed: {result.get('error', 'Unknown error')}")

            # Add delay between accounts
            if i < count - 1:
                delay = uniform(30, 60)  # 30-60 seconds between accounts
                logger.info(f"Waiting {delay:.1f} seconds before next account...")
                time.sleep(delay)

        except Exception as e:
            logger.error(f"Error creating account {i+1}: {e}")
            results.append({"success": False, "error": str(e)})

    return results


def main():
    """Main execution function"""
    import argparse

    parser = argparse.ArgumentParser(description='Gmail Account Creator')
    parser.add_argument('--count', type=int, default=1, help='Number of accounts to create')
    parser.add_argument('--first-name', type=str, help='First name to use for the account')
    parser.add_argument('--headless', action='store_true', help='Run in headless mode')
    parser.add_argument('--config', type=str, help='Path to configuration file')

    args = parser.parse_args()

    # Update configuration based on arguments
    if args.headless:
        gmail_config.set('headless', True)

    if args.config and os.path.exists(args.config):
        try:
            with open(args.config, 'r') as f:
                config_data = json.load(f)
                for key, value in config_data.items():
                    gmail_config.set(key, value)
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")

    # Setup logging
    logger = setup_logging()
    logger.info("Gmail Account Creator started")
    logger.info(f"Configuration: headless={gmail_config.get('headless')}, "
               f"phone_verification={gmail_config.get('use_phone_verification')}")

    try:
        if args.count == 1:
            # Create single account
            result = create_gmail_account(first_name=args.first_name)

            if result['success']:
                print(f"\n✅ Gmail account created successfully!")
                print(f"Email: {result.get('email', 'N/A')}")
                print(f"Password: {result.get('account_data', {}).get('password', 'N/A')}")
            else:
                print(f"\n❌ Failed to create Gmail account: {result.get('error', 'Unknown error')}")

        else:
            # Create multiple accounts
            first_names = [args.first_name] if args.first_name else None
            results = create_multiple_gmail_accounts(args.count, first_names)

            successful = sum(1 for r in results if r['success'])
            print(f"\n📊 Account Creation Summary:")
            print(f"Total attempted: {args.count}")
            print(f"Successful: {successful}")
            print(f"Failed: {args.count - successful}")

            if successful > 0:
                print(f"\n✅ Successfully created accounts:")
                for i, result in enumerate(results):
                    if result['success']:
                        email = result.get('email', 'N/A')
                        password = result.get('account_data', {}).get('password', 'N/A')
                        print(f"  {i+1}. {email} : {password}")

    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"\n❌ Unexpected error: {e}")
    finally:
        logger.info("Gmail Account Creator finished")


if __name__ == "__main__":
    main()